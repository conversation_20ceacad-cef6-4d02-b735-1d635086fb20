export interface Attraction {
  id: number;
  name: string;
  category: string;
  neighborhood: string;
  description: string;
  image?: string;
  link?: string;
  hours?: string;
  price?: string;
  rating?: number;
  metaTitle?: string;
  metaDescription?: string;
  lat?: number;
  lng?: number;
  tips?: string[];
  address?: string;
  phone?: string;
}

export const attractionsData: Attraction[] = [
  {
    id: 5,
    name: "Kennedy Space Center",
    category: "Unique Attractions",
    neighborhood: "East Orlando",
    description: "Explore the past, present, and future of space travel at NASA's launch headquarters with exhibits, tours, and interactive experiences.",
    image: "https://images.unsplash.com/photo-1446776653964-20c1d3a81b06?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=90",
    link: "https://www.kennedyspacecenter.com/",
    hours: "9:00 AM - 5:00 PM",
    price: "$57+",
    rating: 4.7,
    lat: 28.5728,
    lng: -80.649,
    tips: [
      "Arrive early to avoid crowds and get the most out of your visit",
      "Book the KSC Bus Tour for behind-the-scenes access",
      "Don't miss the Space Shuttle Atlantis exhibit"
    ]
  },
  {
    id: 12,
    name: "Lake Eola Park",
    category: "Outdoor Adventures",
    neighborhood: "Downtown Orlando",
    description: "Scenic urban park with a 1-mile walking path around the lake, swan-shaped paddle boats, and a stunning water fountain light show.",
    image: "https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=90",
    link: "https://www.orlando.gov/Parks-the-Environment/Directory/Lake-Eola-Park",
    hours: "6:00 AM - 12:00 AM",
    price: "Free",
    rating: 4.7,
    lat: 28.5414,
    lng: -81.3752,
    tips: [
      "Visit during sunset for the best photo opportunities",
      "Rent a swan boat for a unique lake experience",
      "Check the fountain show schedule for evening light displays"
    ]
  },
  {
    id: 22,
    name: "Fun Spot America",
    category: "Unique Attractions",
    neighborhood: "International Drive",
    description: "Thrill the family at Fun Spot America, a budget-friendly theme park on International Drive. Ride Orlando's only wooden coaster, White Lightning, or soar on the SkyCoaster.",
    image: "https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=90",
    link: "https://fun-spot.com/",
    hours: "10:00 AM - 12:00 AM",
    price: "$59.95",
    rating: 4.6,
    lat: 28.4657,
    lng: -81.4556,
    tips: [
      "Try the White Lightning wooden roller coaster",
      "Visit during weekdays for shorter lines",
      "Check for combo deals with other I-Drive attractions"
    ]
  },
  {
    id: 33,
    name: "ICON Park",
    category: "Unique Attractions",
    neighborhood: "International Drive",
    description: "Entertainment complex featuring The Wheel, Madame Tussauds, SEA LIFE Aquarium, and various dining and shopping options.",
    image: "https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=90",
    link: "https://iconparkorlando.com/",
    hours: "10:00 AM - 10:00 PM",
    price: "$28+",
    rating: 4.3,
    lat: 28.4431,
    lng: -81.4686,
    tips: [
      "Book combo tickets for multiple attractions",
      "Ride The Wheel at sunset for amazing views",
      "Allow 3-4 hours to experience all attractions"
    ]
  },
  {
    id: 44,
    name: "Gatorland",
    category: "Outdoor Adventures",
    neighborhood: "South Orlando",
    description: "The 'Alligator Capital of the World' featuring thousands of alligators and crocodiles, zip lines, and educational shows.",
    image: "https://images.unsplash.com/photo-1546026423-cc4642628d2b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=90",
    link: "https://www.gatorland.com/",
    hours: "10:00 AM - 5:00 PM",
    price: "$29.99+",
    rating: 4.5,
    lat: 28.3553,
    lng: -81.4023,
    tips: [
      "Don't miss the Gator Jumparoo show",
      "Try the Screamin' Gator Zip Line",
      "Bring sunscreen and comfortable walking shoes"
    ]
  },
  {
    id: 55,
    name: "Orlando Science Center",
    category: "Family Fun",
    neighborhood: "Downtown Orlando",
    description: "Interactive science museum with hands-on exhibits, planetarium shows, and educational programs for all ages.",
    image: "https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=90",
    link: "https://www.osc.org/",
    hours: "10:00 AM - 5:00 PM",
    price: "$21+",
    rating: 4.4,
    lat: 28.5383,
    lng: -81.3792,
    tips: [
      "Check the planetarium show schedule",
      "Allow 3-4 hours for a full visit",
      "Great for kids and science enthusiasts"
    ]
  },
  {
    id: 66,
    name: "Bok Tower Gardens",
    category: "Outdoor Adventures",
    neighborhood: "Lake Wales",
    description: "Historic garden sanctuary featuring a 205-foot singing tower, beautiful gardens, and peaceful walking trails.",
    image: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=90",
    link: "https://boktowergardens.org/",
    hours: "8:00 AM - 6:00 PM",
    price: "$15+",
    rating: 4.6,
    lat: 27.9289,
    lng: -81.5820,
    tips: [
      "Visit during carillon concerts for the full experience",
      "Bring a picnic to enjoy in the gardens",
      "Best visited in cooler months"
    ]
  },
  {
    id: 77,
    name: "Blue Spring State Park",
    category: "Outdoor Adventures",
    neighborhood: "Orange City",
    description: "Natural spring park famous for manatee viewing during winter months, with crystal-clear waters perfect for swimming and snorkeling.",
    image: "https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600&q=90",
    link: "https://www.floridastateparks.org/parks-and-trails/blue-spring-state-park",
    hours: "8:00 AM - Sunset",
    price: "$6 per vehicle",
    rating: 4.7,
    lat: 28.9447,
    lng: -81.3431,
    tips: [
      "Visit November-March for manatee viewing",
      "Bring snorkeling gear for the spring run",
      "Arrive early during manatee season"
    ]
  }
];

export const categories = [
  "All",
  "Unique Attractions",
  "Outdoor Adventures", 
  "Family Fun",
  "Entertainment",
  "Shopping",
  "Dining"
];

export const getAttractionsByCategory = (category: string): Attraction[] => {
  if (category === "All") {
    return attractionsData;
  }
  return attractionsData.filter(attraction => attraction.category === category);
};

export const getAttractionById = (id: number): Attraction | undefined => {
  return attractionsData.find(attraction => attraction.id === id);
};

export const getAttractionByName = (name: string): Attraction | undefined => {
  return attractionsData.find(attraction => 
    attraction.name.toLowerCase() === name.toLowerCase()
  );
};

export const searchAttractions = (query: string): Attraction[] => {
  const lowercaseQuery = query.toLowerCase();
  return attractionsData.filter(attraction =>
    attraction.name.toLowerCase().includes(lowercaseQuery) ||
    attraction.description.toLowerCase().includes(lowercaseQuery) ||
    attraction.category.toLowerCase().includes(lowercaseQuery) ||
    attraction.neighborhood.toLowerCase().includes(lowercaseQuery)
  );
};
