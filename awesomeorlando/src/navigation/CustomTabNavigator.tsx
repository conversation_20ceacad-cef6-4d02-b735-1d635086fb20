import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Animated,
  Modal,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Home, MapPin, Utensils, Bed, Calendar, Menu } from 'lucide-react-native';

// Import screens
import HomeScreen from '../screens/HomeScreen';
import ThemeParksScreen from '../screens/ThemeParksScreen';
import DiningScreen from '../screens/DiningScreen';
import HotelsScreen from '../screens/HotelsScreen';
import EventsScreen from '../screens/EventsScreen';
import DrawerContent from './DrawerContent';

const { width } = Dimensions.get('window');

interface TabItem {
  name: string;
  label: string;
  icon: React.ComponentType<any>;
  component: React.ComponentType<any>;
}

const tabs: TabItem[] = [
  { name: 'Home', label: 'Home', icon: Home, component: HomeScreen },
  { name: 'Theme Parks', label: 'Theme Parks', icon: MapPin, component: ThemeParksScreen },
  { name: 'Dining', label: 'Dining', icon: Utensils, component: DiningScreen },
  { name: 'Hotels', label: 'Hotels', icon: Bed, component: HotelsScreen },
  { name: 'Events', label: 'Events', icon: Calendar, component: EventsScreen },
  { name: 'More', label: 'More', icon: Menu, component: View }, // Placeholder component for More
];

const CustomTabNavigator: React.FC = () => {
  const [activeTab, setActiveTab] = useState('Home');
  const [isDrawerVisible, setIsDrawerVisible] = useState(false);
  const slideAnim = useRef(new Animated.Value(-280)).current; // Start off-screen

  const handleTabPress = (tabName: string) => {
    if (tabName === 'More') {
      setIsDrawerVisible(true);
      // Animate drawer in
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    } else {
      setActiveTab(tabName);
    }
  };

  const closeDrawer = () => {
    // Animate drawer out
    Animated.timing(slideAnim, {
      toValue: -280,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setIsDrawerVisible(false);
    });
  };

  const handleDrawerNavigation = (screenName: string) => {
    closeDrawer();
    // Handle navigation to different screens based on drawer selection
    // For now, we'll just close the drawer
  };

  const renderActiveScreen = () => {
    const activeTabData = tabs.find(tab => tab.name === activeTab);
    if (activeTabData && activeTabData.component !== View) {
      const Component = activeTabData.component;
      return <Component />;
    }
    return <View style={{ flex: 1, backgroundColor: '#ffffff' }} />;
  };

  const renderTabIcon = (tab: TabItem, isActive: boolean) => {
    const IconComponent = tab.icon;
    const color = isActive ? '#f97316' : '#6b7280';
    return <IconComponent size={24} color={color} />;
  };

  return (
    <View style={styles.container}>
      {/* Main Content */}
      <View style={styles.content}>
        {renderActiveScreen()}
      </View>

      {/* Custom Tab Bar */}
      <SafeAreaView edges={['bottom']} style={styles.tabBarContainer}>
        <View style={styles.tabBar}>
          {tabs.map((tab) => {
            const isActive = activeTab === tab.name;
            return (
              <TouchableOpacity
                key={tab.name}
                style={styles.tabItem}
                onPress={() => handleTabPress(tab.name)}
                activeOpacity={0.7}
              >
                <View style={styles.tabIconContainer}>
                  {renderTabIcon(tab, isActive)}
                </View>
                <Text style={[
                  styles.tabLabel,
                  { color: isActive ? '#f97316' : '#6b7280' }
                ]}>
                  {tab.label}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>
      </SafeAreaView>

      {/* Drawer Modal */}
      <Modal
        visible={isDrawerVisible}
        transparent={true}
        animationType="none"
        onRequestClose={closeDrawer}
      >
        <View style={styles.modalOverlay}>
          <TouchableOpacity 
            style={styles.modalBackground} 
            activeOpacity={1} 
            onPress={closeDrawer}
          />
          <Animated.View 
            style={[
              styles.drawerContainer,
              { transform: [{ translateX: slideAnim }] }
            ]}
          >
            <DrawerContent 
              onNavigate={handleDrawerNavigation}
              onClose={closeDrawer}
            />
          </Animated.View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  content: {
    flex: 1,
  },
  tabBarContainer: {
    backgroundColor: '#ffffff',
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  tabBar: {
    flexDirection: 'row',
    paddingTop: 5,
    paddingBottom: 5,
    height: 70,
    backgroundColor: '#ffffff',
  },
  tabItem: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
  },
  tabIconContainer: {
    marginBottom: 4,
  },
  tabLabel: {
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
  modalOverlay: {
    flex: 1,
    flexDirection: 'row',
  },
  modalBackground: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  drawerContainer: {
    width: 280,
    backgroundColor: '#ffffff',
    height: '100%',
  },
});

export default CustomTabNavigator;
