import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Image,
  TextInput,
  FlatList,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import {
  MapPin,
  ExternalLink,
  Info,
  Star,
  Search,
  Filter
} from 'lucide-react-native';
import LinearGradient from 'react-native-linear-gradient';
import Header from '../components/Header';
import { attractionsData, categories, getAttractionsByCategory, searchAttractions, Attraction } from '../data/attractions';

const AttractionsScreen: React.FC = () => {
  const navigation = useNavigation();
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  const filteredAttractions = useMemo(() => {
    let attractions = getAttractionsByCategory(selectedCategory);

    if (searchQuery.trim()) {
      attractions = searchAttractions(searchQuery);
      if (selectedCategory !== 'All') {
        attractions = attractions.filter(attraction => attraction.category === selectedCategory);
      }
    }

    return attractions;
  }, [selectedCategory, searchQuery]);

  const openWebsite = (url: string, title: string) => {
    navigation.navigate('WebView' as never, { url, title } as never);
  };

  const openAttractionDetail = (attraction: Attraction) => {
    navigation.navigate('AttractionDetail' as never, { attraction } as never);
  };

  const renderAttractionCard = ({ item: attraction }: { item: Attraction }) => (
    <TouchableOpacity
      style={styles.attractionCard}
      onPress={() => openAttractionDetail(attraction)}
      activeOpacity={0.7}
    >
      <View style={styles.cardImageContainer}>
        <Image
          source={{ uri: attraction.image }}
          style={styles.cardImage}
          resizeMode="cover"
        />
        <View style={styles.cardImageOverlay}>
          <View style={styles.cardImageContent}>
            <Text style={styles.cardTitle} numberOfLines={2}>{attraction.name}</Text>
            <Text style={styles.cardNeighborhood}>{attraction.neighborhood}</Text>
          </View>
        </View>
      </View>

      <View style={styles.cardContent}>
        <View style={styles.cardHeader}>
          <View style={styles.categoryBadge}>
            <Text style={styles.categoryBadgeText}>{attraction.category}</Text>
          </View>
          <View style={styles.locationInfo}>
            <MapPin size={12} color="#6b7280" />
            <Text style={styles.locationText}>{attraction.neighborhood || "Orlando"}</Text>
          </View>
        </View>

        <Text style={styles.cardDescription} numberOfLines={2}>
          {attraction.description}
        </Text>

        <View style={styles.cardFooter}>
          {attraction.link ? (
            <TouchableOpacity
              style={styles.websiteButton}
              onPress={(e) => {
                e.stopPropagation();
                openWebsite(attraction.link!, attraction.name);
              }}
            >
              <ExternalLink size={14} color="#ffffff" />
              <Text style={styles.websiteButtonText}>Website</Text>
            </TouchableOpacity>
          ) : (
            <View style={styles.noWebsiteButton}>
              <ExternalLink size={14} color="#9ca3af" />
              <Text style={styles.noWebsiteButtonText}>No Website</Text>
            </View>
          )}

          <TouchableOpacity
            style={styles.detailsButton}
            onPress={() => openAttractionDetail(attraction)}
          >
            <Info size={14} color="#ffffff" />
            <Text style={styles.detailsButtonText}>Details</Text>
          </TouchableOpacity>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderCategoryFilter = (category: string) => (
    <TouchableOpacity
      key={category}
      style={[
        styles.categoryFilter,
        selectedCategory === category && styles.categoryFilterActive
      ]}
      onPress={() => setSelectedCategory(category)}
    >
      <Text style={[
        styles.categoryFilterText,
        selectedCategory === category && styles.categoryFilterTextActive
      ]}>
        {category}
      </Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <Header />

      {/* Hero Section */}
      <LinearGradient
        colors={['#0d9488', '#0891b2', '#0284c7']}
        style={styles.heroSection}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.heroContent}>
          <View style={styles.heroTitleContainer}>
            <Text style={styles.heroTitle}>
              ORLANDO<Text style={styles.heroTitleAccent}>ATTRACTIONS</Text>
            </Text>
            <View style={styles.heroDecorative} />
          </View>
          <Text style={styles.heroDescription}>
            Discover amazing attractions beyond the theme parks
          </Text>
        </View>
      </LinearGradient>

      {/* Search and Filters */}
      <View style={styles.searchSection}>
        <View style={styles.searchContainer}>
          <Search size={20} color="#6b7280" />
          <TextInput
            style={styles.searchInput}
            placeholder="Search attractions..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            placeholderTextColor="#9ca3af"
          />
        </View>

        <TouchableOpacity
          style={styles.filterButton}
          onPress={() => setShowFilters(!showFilters)}
        >
          <Filter size={20} color="#0891b2" />
        </TouchableOpacity>
      </View>

      {/* Category Filters */}
      {showFilters && (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.categoryFilters}
          contentContainerStyle={styles.categoryFiltersContent}
        >
          {categories.map(renderCategoryFilter)}
        </ScrollView>
      )}

      {/* Results Count */}
      <View style={styles.resultsSection}>
        <Text style={styles.resultsText}>
          Showing {filteredAttractions.length} attractions
          {selectedCategory !== 'All' && ` in ${selectedCategory}`}
        </Text>
      </View>

      {/* Attractions Grid */}
      <FlatList
        data={filteredAttractions}
        renderItem={renderAttractionCard}
        keyExtractor={(item) => item.id.toString()}
        numColumns={2}
        columnWrapperStyle={styles.row}
        contentContainerStyle={styles.attractionsGrid}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  heroSection: {
    paddingHorizontal: 20,
    paddingVertical: 32,
    // position: 'relative',
  },
  heroContent: {
    alignItems: 'center',
  },
  heroTitleContainer: {
    position: 'relative',
    alignItems: 'center',
    marginBottom: 16,
  },
  heroTitle: {
    fontSize: 28,
    fontWeight: '900',
    color: '#ffffff',
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  heroTitleAccent: {
    color: '#fde047',
  },
  heroDecorative: {
    position: 'absolute',
    top: -4,
    right: -8,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#fb7185',
    opacity: 0.8,
  },
  heroDescription: {
    fontSize: 16,
    color: '#ffffff',
    textAlign: 'center',
    opacity: 0.9,
  },
  searchSection: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    gap: 12,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#374151',
  },
  filterButton: {
    backgroundColor: '#f0f9ff',
    borderRadius: 12,
    padding: 12,
    borderWidth: 1,
    borderColor: '#0891b2',
  },
  categoryFilters: {
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  categoryFiltersContent: {
    gap: 8,
  },
  categoryFilter: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f3f4f6',
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  categoryFilterActive: {
    backgroundColor: '#0891b2',
    borderColor: '#0891b2',
  },
  categoryFilterText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6b7280',
  },
  categoryFilterTextActive: {
    color: '#ffffff',
  },
  resultsSection: {
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  resultsText: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
  },
  attractionsGrid: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  row: {
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  attractionCard: {
    flex: 1,
    marginHorizontal: 4,
    backgroundColor: '#ffffff',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
    overflow: 'hidden',
  },
  cardImageContainer: {
    height: 160,
    position: 'relative',
  },
  cardImage: {
    width: '100%',
    height: '100%',
  },
  cardImageOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    padding: 12,
  },
  cardImageContent: {
    flex: 1,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 4,
  },
  cardNeighborhood: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  cardContent: {
    padding: 12,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  categoryBadge: {
    backgroundColor: '#fed7aa',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  categoryBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#ea580c',
  },
  locationInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  locationText: {
    fontSize: 12,
    color: '#6b7280',
  },
  cardDescription: {
    fontSize: 12,
    color: '#6b7280',
    lineHeight: 16,
    marginBottom: 12,
  },
  cardFooter: {
    flexDirection: 'row',
    gap: 8,
  },
  websiteButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#0891b2',
    paddingVertical: 8,
    borderRadius: 6,
    gap: 4,
  },
  websiteButtonText: {
    fontSize: 12,
    color: '#ffffff',
    fontWeight: '500',
  },
  noWebsiteButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f3f4f6',
    paddingVertical: 8,
    borderRadius: 6,
    gap: 4,
  },
  noWebsiteButtonText: {
    fontSize: 12,
    color: '#9ca3af',
    fontWeight: '500',
  },
  detailsButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#ea580c',
    paddingVertical: 8,
    borderRadius: 6,
    gap: 4,
  },
  detailsButtonText: {
    fontSize: 12,
    color: '#ffffff',
    fontWeight: '500',
  },
});

export default AttractionsScreen;