import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Image,
  Dimensions,
  Linking,
} from 'react-native';
import { WebView } from 'react-native-webview';
import { LinearGradient } from 'expo-linear-gradient';
import Header from '../components/Header';

const { width } = Dimensions.get('window');

// Theme park categories
const themeParkCategories = [
  { id: "all", name: "All Parks" },
  { id: "disney", name: "Disney" },
  { id: "universal", name: "Universal" },
  { id: "seaworld", name: "SeaWorld & Discovery Cove" },
  { id: "water-parks", name: "Water Parks" },
  { id: "other", name: "Other Attractions" }
];

// Theme park data structure
interface ThemePark {
  id: string;
  name: string;
  category: string;
  description: string;
  location: string;
  operatingHours: string;
  priceRange: string;
  popularAttractions: string[];
  mainThemes: string[];
  insiderTips: string[];
  websiteUrl: string;
  imageUrl: string;
}

const ThemeParksScreen: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [webViewUrl, setWebViewUrl] = useState<string | null>(null);

  const openWebsite = (url: string) => {
    setWebViewUrl(url);
  };

  const closeWebView = () => {
    setWebViewUrl(null);
  };

  const openExternalLink = async (url: string) => {
    const supported = await Linking.canOpenURL(url);
    if (supported) {
      await Linking.openURL(url);
    }
  };

  if (webViewUrl) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.webViewHeader}>
          <TouchableOpacity onPress={closeWebView} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>✕ Close</Text>
          </TouchableOpacity>
        </View>
        <WebView
          source={{ uri: webViewUrl }}
          style={styles.webView}
          startInLoadingState={true}
        />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Header />
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Hero Section */}
        <LinearGradient
          colors={['#2563eb', '#9333ea', '#ec4899']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.heroSection}
        >
          <View style={styles.heroContent}>
            <View style={styles.heroTitle}>
              <Text style={styles.heroTitleText}>
                ORLANDO<Text style={styles.heroTitleAccent}>THEME PARKS</Text>
              </Text>
            </View>
            <Text style={styles.heroDescription}>
              Discover the magic of world-class theme parks that make Orlando the Theme Park Capital of the World.
            </Text>
            <View style={styles.heroTags}>
              <View style={styles.heroTag}>
                <Text style={styles.heroTagText}>🚀 Thrilling Rides</Text>
              </View>
              <View style={styles.heroTag}>
                <Text style={styles.heroTagText}>✨ Magical Experiences</Text>
              </View>
              <View style={styles.heroTag}>
                <Text style={styles.heroTagText}>🌴 Adventures</Text>
              </View>
            </View>
          </View>
        </LinearGradient>