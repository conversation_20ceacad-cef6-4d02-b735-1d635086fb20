import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Image,
  Dimensions,
  Linking,
} from 'react-native';
import { WebView } from 'react-native-webview';
import Header from '../components/Header';

const { width } = Dimensions.get('window');

// Theme park categories
const themeParkCategories = [
  { id: "all", name: "All Parks" },
  { id: "disney", name: "Disney" },
  { id: "universal", name: "Universal" },
  { id: "seaworld", name: "SeaWorld & Discovery Cove" },
  { id: "water-parks", name: "Water Parks" },
  { id: "other", name: "Other Attractions" }
];

// Theme park data structure
interface ThemePark {
  id: string;
  name: string;
  category: string;
  description: string;
  location: string;
  operatingHours: string;
  priceRange: string;
  popularAttractions: string[];
  mainThemes: string[];
  insiderTips: string[];
  websiteUrl: string;
  imageUrl: string;
}

const ThemeParksScreen: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [webViewUrl, setWebViewUrl] = useState<string | null>(null);

  const openWebsite = (url: string) => {
    setWebViewUrl(url);
  };

  const closeWebView = () => {
    setWebViewUrl(null);
  };

  const openExternalLink = async (url: string) => {
    const supported = await Linking.canOpenURL(url);
    if (supported) {
      await Linking.openURL(url);
    }
  };

  if (webViewUrl) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.webViewHeader}>
          <TouchableOpacity onPress={closeWebView} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>✕ Close</Text>
          </TouchableOpacity>
        </View>
        <WebView
          source={{ uri: webViewUrl }}
          style={styles.webView}
          startInLoadingState={true}
        />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <Header />
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Hero Section */}
        <View style={styles.heroSection}>
          <View style={styles.heroContent}>
            <View style={styles.heroTitle}>
              <Text style={styles.heroTitleText}>
                ORLANDO<Text style={styles.heroTitleAccent}>THEME PARKS</Text>
              </Text>
            </View>
            <Text style={styles.heroDescription}>
              Discover the magic of world-class theme parks that make Orlando the Theme Park Capital of the World.
            </Text>
            <View style={styles.heroTags}>
              <View style={styles.heroTag}>
                <Text style={styles.heroTagText}>🚀 Thrilling Rides</Text>
              </View>
              <View style={styles.heroTag}>
                <Text style={styles.heroTagText}>✨ Magical Experiences</Text>
              </View>
              <View style={styles.heroTag}>
                <Text style={styles.heroTagText}>🌴 Adventures</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Disney World Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <View style={styles.sectionIcon}>
              <Text style={styles.sectionIconText}>✨</Text>
            </View>
            <Text style={styles.sectionTitle}>Walt Disney World Resort</Text>
          </View>

          {/* Disney Main Card */}
          <TouchableOpacity
            style={styles.mainCard}
            onPress={() => openWebsite('https://disneyworld.disney.go.com/')}
          >
            <Image
              source={require('../../assets/images/Donald Duck.jpeg')}
              style={styles.mainCardImage}
              resizeMode="cover"
            />
            <View style={styles.mainCardOverlay}>
              <View style={styles.mainCardContent}>
                <Text style={styles.mainCardTitle}>Walt Disney World</Text>
                <Text style={styles.mainCardDescription}>
                  Experience the magic of four iconic theme parks, two water parks, and Disney Springs in the most magical place on earth.
                </Text>
                <View style={styles.mainCardButton}>
                  <Text style={styles.mainCardButtonText}>✨ Official Site</Text>
                </View>
              </View>
            </View>
          </TouchableOpacity>

          {/* Disney Parks Grid */}
          <View style={styles.parksGrid}>
            <Text style={styles.gridTitle}>✨ Visit the Magic of Disney Parks</Text>

            <View style={styles.gridRow}>
              <TouchableOpacity
                style={[styles.gridItem, styles.gridItemLarge]}
                onPress={() => openWebsite('https://disneyworld.disney.go.com/destinations/magic-kingdom/')}
              >
                <Image
                  source={require('../../assets/images/Donald Duck.jpeg')}
                  style={styles.gridItemImage}
                  resizeMode="cover"
                />
                <View style={styles.gridItemOverlay}>
                  <Text style={styles.gridItemTitle}>Magic Kingdom</Text>
                </View>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.gridItem, styles.gridItemLarge]}
                onPress={() => openWebsite('https://disneyworld.disney.go.com/destinations/epcot/')}
              >
                <Image
                  source={require('../../assets/images/epcot.webp')}
                  style={styles.gridItemImage}
                  resizeMode="cover"
                />
                <View style={styles.gridItemOverlay}>
                  <Text style={styles.gridItemTitle}>EPCOT</Text>
                </View>
              </TouchableOpacity>
            </View>

            <View style={styles.gridRow}>
              <TouchableOpacity
                style={[styles.gridItem, styles.gridItemLarge]}
                onPress={() => openWebsite('https://disneyworld.disney.go.com/destinations/hollywood-studios/')}
              >
                <Image
                  source={require('../../assets/images/Hollywood Studios.jpeg')}
                  style={styles.gridItemImage}
                  resizeMode="cover"
                />
                <View style={styles.gridItemOverlay}>
                  <Text style={styles.gridItemTitle}>Hollywood Studios</Text>
                </View>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.gridItem, styles.gridItemLarge]}
                onPress={() => openWebsite('https://disneyworld.disney.go.com/destinations/animal-kingdom/')}
              >
                <Image
                  source={require('../../assets/images/Animal Kingdom.jpg')}
                  style={styles.gridItemImage}
                  resizeMode="cover"
                />
                <View style={styles.gridItemOverlay}>
                  <Text style={styles.gridItemTitle}>Animal Kingdom</Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Universal Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <View style={[styles.sectionIcon, { backgroundColor: '#f97316' }]}>
              <Text style={styles.sectionIconText}>🎬</Text>
            </View>
            <Text style={styles.sectionTitle}>Universal Orlando Resort</Text>
          </View>

          <View style={styles.parksGrid}>
            <TouchableOpacity
              style={styles.universalCard}
              onPress={() => openWebsite('https://www.universalorlando.com/web/en/us/theme-parks/universal-studios-florida')}
            >
              <Image
                source={require('../../assets/images/Universal Studios Florida.jpeg')}
                style={styles.universalCardImage}
                resizeMode="cover"
              />
              <View style={styles.universalCardOverlay}>
                <Text style={styles.universalCardTitle}>Universal Studios Florida</Text>
                <Text style={styles.universalCardDescription}>
                  Ride the movies with incredible attractions based on blockbuster films and TV shows.
                </Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.universalCard}
              onPress={() => openWebsite('https://www.universalorlando.com/web/en/us/theme-parks/islands-of-adventure')}
            >
              <Image
                source={require('../../assets/images/Islands of Adventure.jpeg')}
                style={styles.universalCardImage}
                resizeMode="cover"
              />
              <View style={styles.universalCardOverlay}>
                <Text style={styles.universalCardTitle}>Islands of Adventure</Text>
                <Text style={styles.universalCardDescription}>
                  Experience legendary adventures in immersive themed islands.
                </Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>

        {/* SeaWorld Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <View style={[styles.sectionIcon, { backgroundColor: '#0891b2' }]}>
              <Text style={styles.sectionIconText}>🌊</Text>
            </View>
            <Text style={styles.sectionTitle}>SeaWorld Parks & Entertainment</Text>
          </View>

          <TouchableOpacity
            style={styles.seaworldMainCard}
            onPress={() => openWebsite('https://seaworld.com/orlando/')}
          >
            <Image
              source={require('../../assets/images/Seaworld.png')}
              style={styles.seaworldMainImage}
              resizeMode="cover"
            />
            <View style={styles.seaworldMainOverlay}>
              <Text style={styles.seaworldMainTitle}>SeaWorld Orlando</Text>
              <Text style={styles.seaworldMainDescription}>
                Experience marine life up close with thrilling rides, educational shows, and conservation experiences.
              </Text>
            </View>
          </TouchableOpacity>

          <View style={styles.seaworldGrid}>
            <TouchableOpacity
              style={styles.seaworldCard}
              onPress={() => openWebsite('https://aquatica.com/orlando/')}
            >
              <Image
                source={require('../../assets/images/Aquatica.jpg')}
                style={styles.seaworldCardImage}
                resizeMode="cover"
              />
              <View style={styles.seaworldCardOverlay}>
                <Text style={styles.seaworldCardTitle}>Aquatica Orlando</Text>
                <Text style={styles.seaworldCardDescription}>
                  Waterpark with high-speed slides and up-close animal experiences.
                </Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.seaworldCard}
              onPress={() => openWebsite('https://discoverycove.com/')}
            >
              <Image
                source={require('../../assets/images/Discovery Cove.jpg')}
                style={styles.seaworldCardImage}
                resizeMode="cover"
              />
              <View style={styles.seaworldCardOverlay}>
                <Text style={styles.seaworldCardTitle}>Discovery Cove</Text>
                <Text style={styles.seaworldCardDescription}>
                  All-inclusive day resort with dolphin swims and snorkeling.
                </Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  scrollView: {
    flex: 1,
  },
  webViewHeader: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: 16,
    backgroundColor: '#f3f4f6',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  closeButton: {
    backgroundColor: '#ef4444',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  closeButtonText: {
    color: '#ffffff',
    fontWeight: '600',
  },
  webView: {
    flex: 1,
  },
  heroSection: {
    backgroundColor: '#2563eb',
    marginHorizontal: 16,
    marginTop: 16,
    marginBottom: 24,
    borderRadius: 12,
    overflow: 'hidden',
  },
  heroContent: {
    padding: 16,
  },
  heroTitle: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    transform: [{ rotate: '-1deg' }],
  },
  heroTitleText: {
    fontSize: 24,
    fontWeight: '800',
    color: '#ffffff',
    textAlign: 'center',
  },
  heroTitleAccent: {
    color: '#fde047',
  },
  heroDescription: {
    fontSize: 14,
    color: '#ffffff',
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 20,
  },
  heroTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: 8,
  },
  heroTag: {
    backgroundColor: 'rgba(147, 51, 234, 0.3)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(147, 51, 234, 0.3)',
  },
  heroTagText: {
    fontSize: 12,
    color: '#ffffff',
    fontWeight: '500',
  },
  section: {
    marginBottom: 32,
    paddingHorizontal: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#2563eb',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  sectionIconText: {
    fontSize: 20,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: '800',
    color: '#1e40af',
    flex: 1,
  },
  mainCard: {
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  mainCardImage: {
    width: '100%',
    height: 300,
  },
  mainCardOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    padding: 20,
  },
  mainCardContent: {
    alignItems: 'flex-start',
  },
  mainCardTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 8,
  },
  mainCardDescription: {
    fontSize: 16,
    color: '#ffffff',
    marginBottom: 16,
    lineHeight: 22,
  },
  mainCardButton: {
    backgroundColor: '#2563eb',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  mainCardButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  parksGrid: {
    marginTop: 16,
  },
  gridTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 12,
  },
  gridRow: {
    flexDirection: 'row',
    marginBottom: 12,
    gap: 12,
  },
  gridItem: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  gridItemLarge: {
    height: 150,
  },
  gridItemImage: {
    width: '100%',
    height: '100%',
  },
  gridItemOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    padding: 12,
    alignItems: 'center',
  },
  gridItemTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#ffffff',
    textAlign: 'center',
  },
  universalCard: {
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  universalCardImage: {
    width: '100%',
    height: 200,
  },
  universalCardOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    padding: 16,
  },
  universalCardTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 8,
  },
  universalCardDescription: {
    fontSize: 14,
    color: '#ffffff',
    lineHeight: 20,
  },
  seaworldMainCard: {
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  seaworldMainImage: {
    width: '100%',
    height: 250,
  },
  seaworldMainOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    padding: 20,
  },
  seaworldMainTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 8,
  },
  seaworldMainDescription: {
    fontSize: 16,
    color: '#ffffff',
    lineHeight: 22,
  },
  seaworldGrid: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 16,
  },
  seaworldCard: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  seaworldCardImage: {
    width: '100%',
    height: 180,
  },
  seaworldCardOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    padding: 12,
  },
  seaworldCardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 4,
  },
  seaworldCardDescription: {
    fontSize: 12,
    color: '#ffffff',
    lineHeight: 16,
  },
});

export default ThemeParksScreen;