{"extends": "@react-native/typescript-config", "compilerOptions": {"resolveJsonModule": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "jsx": "react-jsx", "moduleResolution": "nodenext", "module": "NodeNext", "allowJs": true, "strict": false, "noEmit": true, "isolatedModules": true}, "include": ["src/**/*", "**/*.ts", "**/*.tsx"], "exclude": ["node_modules"]}