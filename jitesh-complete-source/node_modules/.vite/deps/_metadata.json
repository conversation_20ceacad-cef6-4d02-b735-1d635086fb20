{"hash": "77b51d10", "configHash": "e64e0651", "lockfileHash": "2780f761", "browserHash": "67406ba6", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "0a4d9946", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "8119dd94", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "3260bb81", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "01ddd6c8", "needsInterop": true}, "@radix-ui/react-accordion": {"src": "../../@radix-ui/react-accordion/dist/index.mjs", "file": "@radix-ui_react-accordion.js", "fileHash": "dffca7a8", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "3831a0ac", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "027910f8", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "a25b634c", "needsInterop": false}, "@radix-ui/react-popover": {"src": "../../@radix-ui/react-popover/dist/index.mjs", "file": "@radix-ui_react-popover.js", "fileHash": "36d923d8", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "4e9613aa", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "367aa13d", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "48735aea", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "cae14be8", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "12c65a01", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "c34b2800", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "2ec279b0", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "7642c74d", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "6953c028", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "199b1d84", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "c8154bdb", "needsInterop": true}, "react-helmet-async": {"src": "../../react-helmet-async/lib/index.esm.js", "file": "react-helmet-async.js", "fileHash": "4646309c", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "40669ef1", "needsInterop": false}, "wouter": {"src": "../../wouter/esm/index.js", "file": "wouter.js", "fileHash": "0ebe82af", "needsInterop": false}}, "chunks": {"chunk-VKRN2WOV": {"file": "chunk-VKRN2WOV.js"}, "chunk-4ZMUA4SM": {"file": "chunk-4ZMUA4SM.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-BK6LL3XR": {"file": "chunk-BK6LL3XR.js"}, "chunk-BSUAZ7F3": {"file": "chunk-BSUAZ7F3.js"}, "chunk-FMZ2D2ZK": {"file": "chunk-FMZ2D2ZK.js"}, "chunk-KCFVMCIE": {"file": "chunk-KCFVMCIE.js"}, "chunk-PLGJCZBN": {"file": "chunk-PLGJCZBN.js"}, "chunk-XJUVWYEL": {"file": "chunk-XJUVWYEL.js"}, "chunk-QKEGM3G7": {"file": "chunk-QKEGM3G7.js"}, "chunk-J2KA7NLM": {"file": "chunk-J2KA7NLM.js"}, "chunk-E7TSFT4J": {"file": "chunk-E7TSFT4J.js"}, "chunk-RPCDYKBN": {"file": "chunk-RPCDYKBN.js"}, "chunk-KBTYAULA": {"file": "chunk-KBTYAULA.js"}, "chunk-QCHXOAYK": {"file": "chunk-QCHXOAYK.js"}, "chunk-WOOG5QLI": {"file": "chunk-WOOG5QLI.js"}}}