import { useState, useEffect } from "react";
import { <PERSON> } from "wouter";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { MapPin, ExternalLink, Info, Star, Sparkles } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Helmet } from "react-helmet-async";
import { ContextAwareLink } from "@/components/ContextAwareLink";
import AttractionCategoryFilters from "@/components/AttractionCategoryFilters";

// Import static attractions data
import attractionsData from "@/data/attractions.json";

// Diagnostic logging
console.log("Raw attractions count:", attractionsData.length);
console.log("Looking for Mango's in attractions data:");
attractionsData.forEach(a => {
  if (a.name.toLowerCase().includes("mango")) {
    console.log(`Found: ${a.name} (${a.category} in ${a.neighborhood})`);
  }
});

// Find <PERSON><PERSON><PERSON>'s instances
console.log("Checking <PERSON><PERSON><PERSON>'s instances:");
attractionsData.forEach(a => {
  if (a.name.includes("Ripley")) {
    console.log(`Ripley's instance: ${a.name}, ID: ${a.id}, Category: ${a.category}`);
  }
});

// Define attraction type with extended fields for dinner shows
type Attraction = {
  id: number;
  name: string;
  category: string;
  neighborhood: string;
  image: string;
  description: string;
  rating?: number;
  link?: string;
  hours?: string;
  price?: string;
  address?: string;
  fullDescription?: string;
};

// List of attractions to exclude from the Unique Attractions tab
const attractionsToExclude = [
  "Pirates Dinner Adventure", 
  "Outta Control Magic Comedy Dinner Show",
  "The Outta Control Magic Comedy Dinner Show", // Added version with "The" prefix
  "Sleuths Mystery Dinner Show", // Fixed name - removed plural "Shows"
  "Medieval Times Dinner & Tournament", 
  "Hoop-Dee-Doo Musical Revue", 
  "Disney's Spirit of Aloha Dinner Show",
  "Epcot",
  "Disney's Hollywood Studios",
  "Islands of Adventure",
  "Epic Universe",
  "Hollywood Studios", 
  "Disney Hollywood Studios",
  // Add WonderWorks to exclude since the Outta Control Magic Comedy Dinner Show is there
  "WonderWorks",
  // Add the user-requested attractions to exclude
  "Artifact Candle Foundry",
  "Moss Art Class",
  "Pearl Express Train",
  "Mirror Maze at ICON Park",
  "Hamlin House Paddle and Social Club",
  // Add the second Ripley's entry with ID 282
  "Ripley's Believe It or Not! Orlando (ID 282)",
  // Remove World of Chocolate Museum & Café as requested by the user
  "World of Chocolate Museum & Café",
  // Remove Tampa Bay attractions as requested by the user
  "Busch Gardens Tampa Bay",
  "Dinosaur World",
  "ZooTampa at Lowry Park",
  "Wandering Palm Food Tours",
  "Teatro Martini Orlando",
  "Mango's Tropical Cafe",
  // Remove from dynamic data to hardcode instead
  "World of Chocolate Museum & Café",
  // Remove The Pinball Palace as requested by the user
  "The Pinball Palace",
  // Remove Orlando Auto Museum as requested by the user
  "Orlando Auto Museum",
  // Remove Fun Spot Arcade as requested by the user
  "Fun Spot Arcade",
  // Move Wantilan Luau to Dinner Shows as requested by the user
  "Wantilan Luau",
  // Remove Skeletons: Museum of Osteology as requested by the user
  "Skeletons: Museum of Osteology",
  // Remove United Arts of Central Florida Art & Food Walking Tours as requested by the user
  "United Arts of Central Florida Art & Food Walking Tours",
  // Remove 7D Dark Ride Adventure as requested by the user
  "7D Dark Ride Adventure",
  // Remove WhirlyDome as requested by the user
  "WhirlyDome",
  // Remove GlowZone Orlando as requested by the user
  "GlowZone Orlando",
  // Remove Hollywood Drive-In Golf as requested by the user
  "Hollywood Drive-In Golf",
  // America's Escape is now displayed
  // "America's Escape",
  // Remove Orlando StarFlyer as requested by the user
  "Orlando StarFlyer",
  // Remove Wild Florida as requested by the user
  "Wild Florida",
  // Only remove duplicate Fun Spot America entry but keep one instance
  // Note: The actual filtering is done in the uniqueAttractions filter function
];

// Filter attractions for each category
const uniqueAttractions = attractionsData.filter(a => {
  // Skip Mango's Tropical Cafe explicitly with extra detailed logging
  console.log(`Checking attraction: "${a.name}" (matches: ${a.name === "Mango's Tropical Cafe"})`);
  
  if (a.name === "Mango's Tropical Cafe" || a.name.includes("Mango") || a.name.includes("Tropical Cafe")) {
    console.log(`EXCLUDING "${a.name}" from Unique Attractions`);
    return false;
  }
  
  // Skip The Pinball Palace as requested by the user
  if (a.name === "The Pinball Palace" || a.name.includes("Pinball Palace")) {
    console.log(`EXCLUDING "${a.name}" from Unique Attractions - user requested removal`);
    return false;
  }
  
  // Skip Fun Spot Arcade as requested by the user
  if (a.name === "Fun Spot Arcade" || a.name.includes("Fun Spot Arcade")) {
    console.log(`EXCLUDING "${a.name}" from Unique Attractions - user requested removal`);
    return false;
  }
  
  // Skip Wantilan Luau as requested by the user - move to Dinner Shows
  if (a.name === "Wantilan Luau" || a.name.includes("Wantilan Luau")) {
    console.log(`EXCLUDING "${a.name}" from Unique Attractions - moving to Dinner Shows`);
    return false;
  }
  
  // Skip Skeletons: Museum of Osteology as requested by the user
  if (a.name === "Skeletons: Museum of Osteology" || a.name.includes("Skeletons: Museum") || a.name.includes("Museum of Osteology")) {
    console.log(`EXCLUDING "${a.name}" from Unique Attractions - user requested removal`);
    return false;
  }
  
  // Skip United Arts of Central Florida Art & Food Walking Tours as requested by the user
  if (a.name === "United Arts of Central Florida Art & Food Walking Tours" || a.name.includes("United Arts") || a.name.includes("Art & Food Walking Tours")) {
    console.log(`EXCLUDING "${a.name}" from Unique Attractions - user requested removal`);
    return false;
  }
  
  // Skip 7D Dark Ride Adventure as requested by the user
  if (a.name === "7D Dark Ride Adventure" || a.name.includes("7D Dark Ride") || a.name.includes("Dark Ride Adventure")) {
    console.log(`EXCLUDING "${a.name}" from Unique Attractions - user requested removal`);
    return false;
  }
  
  // Skip WhirlyDome as requested by the user
  if (a.name === "WhirlyDome" || a.name.includes("WhirlyDome") || a.name.includes("Whirly Dome")) {
    console.log(`EXCLUDING "${a.name}" from Unique Attractions - user requested removal`);
    return false;
  }
  
  // Skip GlowZone Orlando as requested by the user
  if (a.name === "GlowZone Orlando" || a.name.includes("GlowZone") || a.name.includes("Glow Zone")) {
    console.log(`EXCLUDING "${a.name}" from Unique Attractions - user requested removal`);
    return false;
  }
  
  // Skip Hollywood Drive-In Golf as requested by the user
  if (a.name === "Hollywood Drive-In Golf" || a.name.includes("Hollywood Drive-In") || a.name.includes("Drive-In Golf")) {
    console.log(`EXCLUDING "${a.name}" from Unique Attractions - user requested removal`);
    return false;
  }
  
  // America's Escape is now displayed
  /* if (a.name === "America's Escape" || a.name.includes("America's Escape") || a.name.includes("Americas Escape")) {
    console.log(`EXCLUDING "${a.name}" from Unique Attractions - user requested removal`);
    return false;
  } */
  
  // Skip Orlando StarFlyer as requested by the user
  if (a.name === "Orlando StarFlyer" || a.name.includes("StarFlyer")) {
    console.log(`EXCLUDING "${a.name}" from Unique Attractions - user requested removal`);
    return false;
  }
  
  // Skip World of Chocolate Museum & Café as requested by the user
  if (a.name === "World of Chocolate Museum & Café" || a.name.includes("World of Chocolate") || a.name.includes("Chocolate Museum")) {
    console.log(`EXCLUDING "${a.name}" from Unique Attractions - user requested removal`);
    return false;
  }
  
  // Skip Tampa Bay attractions as requested by the user
  if (a.name.includes("Busch Gardens") || a.name.includes("Tampa Bay")) {
    console.log(`EXCLUDING "${a.name}" from Unique Attractions - user requested removal of Tampa Bay attractions`);
    return false;
  }
  
  if (a.name.includes("Dinosaur World")) {
    console.log(`EXCLUDING "${a.name}" from Unique Attractions - user requested removal of Tampa Bay attractions`);
    return false;
  }
  
  if (a.name.includes("ZooTampa") || a.name.includes("Lowry Park")) {
    console.log(`EXCLUDING "${a.name}" from Unique Attractions - user requested removal of Tampa Bay attractions`);
    return false;
  }
  
  // Handle Ripley's Believe It or Not! entries - more broad match of name
  if (a.name.includes("Ripley") || a.name.includes("Believe It or Not")) {
    // Just use ID to filter - keep only one instance
    console.log(`Checking Ripley's with ID ${a.id}`);
    
    // Keep only one specific Ripley's entry - use ID for reliable filtering
    if (a.id === 108) {
      console.log(`KEEPING Ripley's with ID ${a.id} in Unique Attractions`);
      return true;
    } else {
      console.log(`EXCLUDING Ripley's with ID ${a.id} from Unique Attractions`);
      return false;
    }
  }
  
  // Skip specific Fun Spot America entries, keeping only the original
  if (a.name === "Fun Spot America Orlando" || 
      a.name === "Fun Spot America Kissimmee" ||
      (a.name === "Fun Spot America" && a.description && a.description.includes("Fun Spot America Orlando"))) {
    console.log(`EXCLUDING "${a.name}" from Unique Attractions - user requested removal of specific duplicate entry`);
    return false;
  }
  
  // More thorough check to exclude Hollywood Studios or any attraction containing "Hollywood Studios" in the name
  const isHollywoodStudios = a.name.includes("Hollywood Studios") || 
                            a.name.toLowerCase().includes("hollywood studios");
  
  // Check for dinner shows by name (in case the category in JSON is wrong)
  const isDinnerShow = a.name.includes("Dinner Show") || 
                       a.name.toLowerCase().includes("dinner show");
  
  // More robust check for excluded attractions (case-insensitive)
  const isExcluded = attractionsToExclude.some(excluded => 
    a.name.toLowerCase() === excluded.toLowerCase() ||
    a.name.toLowerCase().includes(excluded.toLowerCase())
  );
  
  // Log for debugging
  if (a.name.toLowerCase().includes("mango") || a.category === "Dinner Shows") {
    console.log(`Checking: ${a.name} (${a.category}), Excluded: ${isExcluded}`);
  }
  
  return a.category === "Unique Attractions" && 
         !isExcluded &&
         !isDinnerShow &&
         !isHollywoodStudios;
});
// Filter Outdoor Adventures - filtering out Adventure Island (Tampa Bay attraction)
const outdoorAttractions = attractionsData.filter(a => {
  // First check if it's the right category
  if (a.category !== "Outdoor Adventures") {
    return false;
  }
  
  // Skip Adventure Island as requested by the user
  if (a.name === "Adventure Island" || a.name.includes("Adventure Island")) {
    console.log(`EXCLUDING "${a.name}" from Outdoor Adventures - user requested removal of Tampa Bay attraction`);
    return false;
  }
  
  // Skip Paddleboard Orlando as requested by the user
  if (a.name === "Paddleboard Orlando" || a.name.includes("Paddleboard Orlando")) {
    console.log(`EXCLUDING "${a.name}" from Outdoor Adventures - user requested removal`);
    return false;
  }
  
  // Skip Orlando Balloon Rides as requested by the user
  if (a.name === "Orlando Balloon Rides" || a.name.includes("Orlando Balloon Rides")) {
    console.log(`EXCLUDING "${a.name}" from Outdoor Adventures - user requested removal`);
    return false;
  }
  
  // Skip Kelly Park as requested by the user
  if (a.name === "Kelly Park" || a.name.includes("Kelly Park")) {
    console.log(`EXCLUDING "${a.name}" from Outdoor Adventures - user requested removal`);
    return false;
  }
  
  // Skip Orlando Wetlands Park as requested by the user
  if (a.name === "Orlando Wetlands Park" || a.name.includes("Orlando Wetlands Park")) {
    console.log(`EXCLUDING "${a.name}" from Outdoor Adventures - user requested removal`);
    return false;
  }
  
  // Skip Wild Florida as requested by the user
  if (a.name === "Wild Florida" || a.name.includes("Wild Florida")) {
    console.log(`EXCLUDING "${a.name}" from Outdoor Adventures - user requested removal`);
    return false;
  }
  
  // Skip Black Hammock Airboat Rides as requested by the user
  if (a.name === "Black Hammock Airboat Rides" || a.name.includes("Black Hammock")) {
    console.log(`EXCLUDING "${a.name}" from Outdoor Adventures - user requested removal`);
    return false;
  }
  
  // Skip Crystal River Watersports as requested by the user
  if (a.name === "Crystal River Watersports" || a.name.includes("Crystal River")) {
    console.log(`EXCLUDING "${a.name}" from Outdoor Adventures - user requested removal`);
    return false;
  }
  
  // Skip Wekiva Island as requested by the user
  if (a.name === "Wekiva Island" || a.name.includes("Wekiva Island")) {
    console.log(`EXCLUDING "${a.name}" from Outdoor Adventures - user requested removal`);
    return false;
  }
  
  // Skip Silver Springs as requested by the user
  if (a.name === "Silver Springs State Park" || a.name.includes("Silver Springs")) {
    console.log(`EXCLUDING "${a.name}" from Outdoor Adventures - user requested removal`);
    return false;
  }
  
  // Skip Wild Willy's Airboat Tours as requested by the user
  if (a.name === "Wild Willy's Airboat Tours" || a.name.includes("Wild Willy")) {
    console.log(`EXCLUDING "${a.name}" from Outdoor Adventures - user requested removal`);
    return false;
  }
  
  // Skip Airboat Adventures by Marsh Landing as requested by the user
  if (a.name === "Airboat Adventures by Marsh Landing" || a.name.includes("Marsh Landing")) {
    console.log(`EXCLUDING "${a.name}" from Outdoor Adventures - user requested removal`);
    return false;
  }
  
  // Skip Sanford RiverWalk as requested by the user
  if (a.name === "Sanford RiverWalk" || a.name.includes("Sanford RiverWalk")) {
    console.log(`EXCLUDING "${a.name}" from Outdoor Adventures - user requested removal`);
    return false;
  }
  
  // Skip Woods ATV Rentals as requested by the user
  if (a.name === "Woods ATV Rentals" || a.name.includes("Woods ATV")) {
    console.log(`EXCLUDING "${a.name}" from Outdoor Adventures - user requested removal`);
    return false;
  }
  
  return true;
});
// Filter Cultural/Educational attractions
const culturalAttractions = attractionsData.filter(a => {
  // First check if it's the right category
  if (a.category !== "Cultural/Educational") {
    return false;
  }
  
  // Skip Historic Leu House Museum as requested by the user
  if (a.name === "Historic Leu House Museum" || a.name.includes("Leu House")) {
    console.log(`EXCLUDING "${a.name}" from Cultural/Educational - user requested removal`);
    return false;
  }
  
  // Skip Sanford Museum as requested by the user
  if (a.name === "Sanford Museum" || a.name.includes("Sanford Museum")) {
    console.log(`EXCLUDING "${a.name}" from Cultural/Educational - user requested removal`);
    return false;
  }
  
  // Skip Historic Eatonville Walking Tour as requested by the user
  if (a.name === "Historic Eatonville Walking Tour" || a.name.includes("Eatonville Walking Tour")) {
    console.log(`EXCLUDING "${a.name}" from Cultural/Educational - user requested removal`);
    return false;
  }
  
  // Skip Dr. Phillips Center for the Performing Arts as requested by the user
  if (a.name === "Dr. Phillips Center for the Performing Arts" || a.name.includes("Dr. Phillips Center")) {
    console.log(`EXCLUDING "${a.name}" from Cultural/Educational - user requested removal`);
    return false;
  }
  
  return true;
});

// Define our new hardcoded attractions
// 1. Tank America
const tankAmericaAttraction = {
  id: 2001,
  name: "Tank America",
  category: "Unique Attractions",
  neighborhood: "East Orlando",
  image: "/images/attractions/tank-america.jpg",
  description: "Unique adventure experience where guests can drive actual military tanks on a rugged course, with additional military-themed activities.",
  rating: 4.9,
  link: "https://www.tankamerica.com/",
  hours: "Thursday - Monday, 9:00 AM - 5:00 PM (closed Tue-Wed)",
  price: "$349 - $549 for tank driving experiences, $35+ for other activities",
  address: "6605 Muskogee Street, Orlando, FL 32807",
  fullDescription: "Tank America in Orlando offers the rare opportunity to drive real military tanks on a custom-designed tactical training facility. The signature experience lets guests command an FV433 Abbott tank across challenging obstacles and terrain. Additional offerings include military vehicle rides, tactical laser tag in a themed battlefield, and an outdoor gun range. Perfect for thrill-seekers, Tank America provides comprehensive training and supervision to ensure a safe yet exhilarating adventure. It's a truly unique attraction in East Orlando that creates unforgettable memories for military enthusiasts and adventure seekers alike."
};

// 2. The Escape Game Orlando
const escapeGameAttraction = {
  id: 2002,
  name: "The Escape Game Orlando",
  category: "Unique Attractions",
  neighborhood: "International Drive",
  image: "/images/attractions/the-escape-game-orlando.jpeg",
  description: "Award-winning escape room experience featuring cinema-quality set designs and immersive storytelling across seven unique adventure themes, offering the ultimate puzzle-solving challenge for friends, families, and teams.",
  rating: 4.8,
  link: "https://theescapegame.com/orlando/",
  hours: "9:00 AM - 11:30 PM daily",
  price: "$36.99 - $44.99 per person",
  address: "8145 International Dr, Orlando, FL 32819",
  fullDescription: "The Escape Game Orlando offers the ultimate adventure for puzzle enthusiasts and thrill-seekers in the heart of International Drive. This premium attraction features meticulously crafted, fully immersive environments that transport guests into extraordinary worlds and scenarios. Each of their signature 60-minute adventures challenges teams to work together, using observation, critical thinking, and teamwork to solve intricate puzzles and accomplish their mission before time runs out. With stunning production values, cutting-edge technology, and masterful storytelling, The Escape Game creates unforgettable experiences that appeal to friends, families, and corporate groups alike. Their thoughtfully designed scenarios range from breaking out of prison to finding missing gold, navigating outer space, or conducting special operations, offering a perfect blend of excitement, suspense, and satisfaction when you finally solve the final puzzle."
};

// 3. Escapology
const escapologyAttraction = {
  id: 2003,
  name: "Escapology",
  category: "Unique Attractions",
  neighborhood: "International Drive",
  image: "images/attractions/escapology.jpeg",
  description: "Sophisticated escape room experience featuring elaborately designed themed environments, authentic props, and compelling storylines that fully immerse players in scenarios from solving mansion murders to preventing viral outbreaks.",
  rating: 4.7,
  link: "https://www.escapology.com/en/orlando-fl",
  hours: "12:00 PM - 11:00 PM (Sun-Thu), 12:00 PM - 1:00 AM (Fri-Sat)",
  price: "$29.99 - $34.99 per person",
  address: "11978 International Drive, Orlando, FL 32821",
  fullDescription: "Escapology Orlando offers sophisticated, high-end escape room experiences with meticulous attention to detail in their themed rooms. Players are fully immersed in scenarios such as solving a murder in a mansion, escaping from a submarine, or preventing a viral outbreak. Each game presents a series of challenging puzzles that must be solved within a 60-minute timeframe. Located near the Orlando Eye, Escapology features authentic props, advanced technology, and compelling storylines that make players feel like they've stepped into a movie set. The experiences are designed for groups of 2-8 people and range from moderate to difficult, providing an exciting challenge for both newcomers and experienced escape room enthusiasts."
};

// 4. Ripley's Believe It or Not! Orlando
const ripleysBelieveItOrNotAttraction = {
  id: 2004,
  name: "Ripley's Believe It or Not! Orlando",
  category: "Unique Attractions",
  neighborhood: "International Drive",
  image: "/images/attractions/ripleys-believe-it-or-not-orlando.jpeg",
  description: "Famous 'odditorium' housed in a building designed to appear sinking into the ground, featuring over 600 exhibits of bizarre artifacts, interactive displays, and mind-bending phenomena across 16 themed galleries spanning three floors.",
  rating: 4.5,
  link: "https://www.ripleys.com/orlando/",
  hours: "10:00 AM - 11:00 PM daily (last admission at 10:00 PM)",
  price: "$24.99 - $34.99 (discounts available online)",
  address: "8201 International Dr, Orlando, FL 32819",
  fullDescription: "Ripley's Believe It or Not! Orlando is a fascinating 'odditorium' housed in a building designed to appear as if it's sinking into the ground due to a Florida sinkhole. Inside, visitors will discover over 600 exhibits and artifacts spanning 16 themed galleries across three floors. The collection features everything from genuine shrunken heads and a vampire killing kit to a scale model of the Titanic made from matchsticks and optical illusions that challenge perception. Interactive displays allow guests to measure themselves against the world's tallest man, step inside a tornado simulation, and experience mind-bending visual phenomena. Located on International Drive, this unique museum celebrates the strange, unusual, and extraordinary from around the globe, making it a perfect counterbalance to Orlando's theme parks."
};

// 5. Museum of Illusions Orlando
const museumOfIllusionsAttraction = {
  id: 2005,
  name: "Museum of Illusions Orlando",
  category: "Unique Attractions",
  neighborhood: "ICON Park",
  image: "/images/attractions/museum-of-illusions-orlando.jpeg",
  description: "Mind-bending interactive museum at ICON Park featuring over 50 immersive exhibits and rooms that challenge perception through optical illusions, visual paradoxes, and sensory experiences designed for both entertainment and education.",
  rating: 4.6,
  link: "https://orlando.museumofillusions.us/",
  hours: "10:00 AM - 10:00 PM (Sun-Thu), 10:00 AM - 12:00 AM (Fri-Sat)",
  price: "$24.99 for adults, $19.99 for children (5-12)",
  address: "8441 International Dr Suite 250, Orlando, FL 32819",
  fullDescription: "The Museum of Illusions Orlando offers a unique, mind-bending experience with over 50 exhibits that challenge perception and play with the senses. Located at ICON Park on International Drive, this museum features rooms and displays that create striking visual paradoxes and optical illusions. Visitors can appear to shrink or grow in the Ames Room, defy gravity in the Reverse Room, see their head served on a platter in the Head on a Platter exhibit, or get lost in the infinity rooms. Each exhibit is designed to be both entertaining and educational, explaining the science and mathematics behind how our brains process visual information. Perfect for families, friends, and social media enthusiasts looking for unique photo opportunities, the Museum of Illusions provides an interactive experience that's both engaging and thought-provoking."
};

// 6. K1 Speed Orlando
const k1SpeedAttraction = {
  id: 2006,
  name: "K1 Speed Orlando",
  category: "Unique Attractions",
  neighborhood: "International Drive",
  image: "/images/attractions/k1-speed-orlando.jpeg",
  description: "Race like a pro on European-style tracks in eco-friendly electric karts reaching speeds up to 45 mph at this premier indoor karting facility. Experience instant torque, challenging turns, and computerized timing systems that track your performance down to the thousandth of a second.",
  rating: 4.7,
  link: "https://www.k1speed.com/orlando-location.html",
  address: "9550 Parksouth Ct Suite 1000, Orlando, FL 32837",
  fullDescription: "K1 Speed Orlando offers a premium indoor go-kart racing experience with all-electric karts that deliver instant torque and speeds up to 45 mph. The facility features a professionally designed track with challenging turns and straightaways that test drivers' skills and reflexes. Unlike traditional gas-powered karts, K1's electric vehicles produce zero emissions, making the indoor environment clean and comfortable while still providing the adrenaline rush of competitive racing. The venue offers separate junior karts for younger racers (48\" minimum height), making it a family-friendly attraction. Beyond racing, K1 Speed includes a lounge area with video games, pool tables, and a Paddock Lounge serving food and beverages. Real-time scoring, race statistics, and podium ceremonies add to the authentic motorsport experience, making K1 Speed popular with both tourists and locals seeking high-speed entertainment."
};

// 7. Topgolf Orlando
const topgolfAttraction = {
  id: 2007,
  name: "Topgolf Orlando",
  category: "Unique Attractions",
  neighborhood: "International Drive",
  image: "/images/attractions/topgolf-orlando.jpeg",
  description: "Reimagined golf experience where players of all skill levels hit microchipped balls at illuminated targets from luxury climate-controlled bays. This 240-yard, three-story entertainment complex combines competitive play with premium dining, full bars, and a high-energy social atmosphere.",
  rating: 4.6,
  link: "https://topgolf.com/us/orlando/",
  address: "9295 Universal Blvd, Orlando, FL 32819",
  fullDescription: "Topgolf Orlando combines the classic driving range experience with modern technology and entertainment elements to create a unique attraction suitable for golfers and non-golfers alike. The three-story facility features 102 climate-controlled hitting bays where players hit microchipped golf balls at illuminated targets on the 240-yard outfield. Each bay includes comfortable seating, HDTVs, and full food and beverage service. The scoring system tracks each shot's accuracy and distance, turning practice into competitive games suitable for all skill levels. Beyond golf, the venue offers a rooftop terrace with fire pits, over 200 HDTVs throughout the facility, a full-service restaurant and bars, and event spaces. Located near Universal Orlando Resort on Universal Boulevard, Topgolf provides a social, interactive experience that's particularly popular for group outings, corporate events, and casual entertainment."
};

// First check if any of our new attractions already exist in uniqueAttractions
const existingNames = uniqueAttractions.map(a => a.name);

// Only add attractions that aren't already in the list (by name)
const newAttractions = [
  tankAmericaAttraction,
  escapeGameAttraction, 
  escapologyAttraction,
  ripleysBelieveItOrNotAttraction,
  museumOfIllusionsAttraction,
  k1SpeedAttraction,
  topgolfAttraction
].filter(a => !existingNames.includes(a.name));

// Create a combined array of unique attractions
const allUniqueAttractions = [
  ...uniqueAttractions,
  ...newAttractions
];

// Verify if attractions are still in the uniqueAttractions or allUniqueAttractions
console.log("VERIFYING FINAL FILTERED LISTS:");
console.log("Is Mango's in uniqueAttractions?", uniqueAttractions.some(a => a.name.includes("Mango")));
console.log("Is Mango's in allUniqueAttractions?", allUniqueAttractions.some(a => a.name.includes("Mango")));
console.log("Is Pinball Palace in uniqueAttractions?", uniqueAttractions.some(a => a.name.includes("Pinball")));
console.log("Is Pinball Palace in allUniqueAttractions?", allUniqueAttractions.some(a => a.name.includes("Pinball")));
console.log("Is Fun Spot Arcade in uniqueAttractions?", uniqueAttractions.some(a => a.name.includes("Fun Spot Arcade")));
console.log("Is Fun Spot Arcade in allUniqueAttractions?", allUniqueAttractions.some(a => a.name.includes("Fun Spot Arcade")));
console.log("Is Skeletons Museum in uniqueAttractions?", uniqueAttractions.some(a => a.name.includes("Skeletons")));
console.log("Is Skeletons Museum in allUniqueAttractions?", allUniqueAttractions.some(a => a.name.includes("Skeletons")));
console.log("Is United Arts in uniqueAttractions?", uniqueAttractions.some(a => a.name.includes("United Arts")));
console.log("Is United Arts in allUniqueAttractions?", allUniqueAttractions.some(a => a.name.includes("United Arts")));
console.log("Is 7D Dark Ride in uniqueAttractions?", uniqueAttractions.some(a => a.name.includes("7D Dark")));
console.log("Is 7D Dark Ride in allUniqueAttractions?", allUniqueAttractions.some(a => a.name.includes("7D Dark")));
console.log("Is GlowZone in uniqueAttractions?", uniqueAttractions.some(a => a.name.includes("GlowZone")));
console.log("Is GlowZone in allUniqueAttractions?", allUniqueAttractions.some(a => a.name.includes("GlowZone")));
console.log("Is Hollywood Drive-In Golf in uniqueAttractions?", uniqueAttractions.some(a => a.name.includes("Hollywood Drive-In")));
console.log("Is Hollywood Drive-In Golf in allUniqueAttractions?", allUniqueAttractions.some(a => a.name.includes("Hollywood Drive-In")));
console.log("Is America's Escape in uniqueAttractions?", uniqueAttractions.some(a => a.name.includes("America's Escape")));
console.log("Is America's Escape in allUniqueAttractions?", allUniqueAttractions.some(a => a.name.includes("America's Escape")));
console.log("Is Fun Spot America in uniqueAttractions?", uniqueAttractions.some(a => a.name.includes("Fun Spot America")));
console.log("Is Fun Spot America in allUniqueAttractions?", allUniqueAttractions.some(a => a.name.includes("Fun Spot America")));
console.log("Is Orlando StarFlyer in uniqueAttractions?", uniqueAttractions.some(a => a.name.includes("StarFlyer")));
console.log("Is Orlando StarFlyer in allUniqueAttractions?", allUniqueAttractions.some(a => a.name.includes("StarFlyer")));
console.log("How many Ripley's instances in uniqueAttractions?", uniqueAttractions.filter(a => a.name.includes("Ripley")).length);
console.log("How many Ripley's instances in allUniqueAttractions?", allUniqueAttractions.filter(a => a.name.includes("Ripley")).length);
console.log("Is World of Chocolate in uniqueAttractions?", uniqueAttractions.some(a => a.name.includes("World of Chocolate") || a.name.includes("Chocolate Museum")));
console.log("Is World of Chocolate in allUniqueAttractions?", allUniqueAttractions.some(a => a.name.includes("World of Chocolate") || a.name.includes("Chocolate Museum")));
console.log("Is Busch Gardens Tampa Bay in uniqueAttractions?", uniqueAttractions.some(a => a.name.includes("Busch Gardens")));
console.log("Is Dinosaur World in uniqueAttractions?", uniqueAttractions.some(a => a.name.includes("Dinosaur World")));
console.log("Is ZooTampa in uniqueAttractions?", uniqueAttractions.some(a => a.name.includes("ZooTampa") || a.name.includes("Lowry Park")));
console.log("Is Adventure Island in outdoorAttractions?", outdoorAttractions.some(a => a.name.includes("Adventure Island")));
console.log("Is Paddleboard Orlando in outdoorAttractions?", outdoorAttractions.some(a => a.name.includes("Paddleboard Orlando")));
console.log("Is Orlando Balloon Rides in outdoorAttractions?", outdoorAttractions.some(a => a.name.includes("Orlando Balloon Rides")));
console.log("Is Kelly Park in outdoorAttractions?", outdoorAttractions.some(a => a.name.includes("Kelly Park")));
console.log("Is Orlando Wetlands Park in outdoorAttractions?", outdoorAttractions.some(a => a.name.includes("Orlando Wetlands Park")));
console.log("Is Historic Leu House Museum in culturalAttractions?", culturalAttractions.some(a => a.name.includes("Leu House")));
console.log("Is Sanford Museum in culturalAttractions?", culturalAttractions.some(a => a.name.includes("Sanford Museum")));
console.log("Is Historic Eatonville Walking Tour in culturalAttractions?", culturalAttractions.some(a => a.name.includes("Eatonville Walking")));
console.log("Is Dr. Phillips Center in culturalAttractions?", culturalAttractions.some(a => a.name.includes("Dr. Phillips Center")));

// Define our categories with their expected counts
const CATEGORIES = [
  { name: "Unique Attractions", count: allUniqueAttractions.length },
  { name: "Outdoor Adventures", count: outdoorAttractions.length },
  { name: "Cultural/Educational", count: culturalAttractions.length },
  { name: "Dinner Shows", count: 8 } // Updated count after removing Disney's Spirit of Aloha
];

// Hard-coded dinner show attractions with complete information
const dinnerShowAttractions = [
  {
    id: 1001,
    name: "Medieval Times Dinner & Tournament",
    category: "Dinner Shows",
    neighborhood: "Kissimmee",
    image: "/images/dinner-shows/medieval-times-dinner-tournament.jpeg",
    description: "Experience an 11th-century feast and tournament with knights competing in jousting and sword fighting while you dine on a four-course meal.",
    rating: 4.7,
    link: "https://www.medievaltimes.com/location/florida/",
    hours: "Show times vary, typically 6:00 PM & 8:30 PM",
    price: "Adults: $65.95 - $85.95, Children (12 & under): $40.95 - $45.95",
    address: "4510 W Vine St, Kissimmee, FL 34746",
    fullDescription: "Medieval Times Dinner & Tournament is a dinner theater featuring staged medieval-style games, sword-fighting, and jousting performed by a cast of knights. Located in Kissimmee, Florida, just minutes from Orlando's major attractions, this two-hour live action spectacle has been entertaining guests since 1983. Throughout the tournament, you'll be served a four-course 'hands-on' feast fit for royalty including tomato bisque soup, roasted chicken, buttered corn, herb-basted potato, and dessert pastry."
  },
  {
    id: 1002,
    name: "Hoop-Dee-Doo Musical Revue",
    category: "Dinner Shows",
    neighborhood: "Lake Buena Vista",
    image: "/images/dinner-shows/hoop-dee-doo-musical-revue.jpeg",
    description: "A long-running Disney dinner show featuring Wild West-themed musical performances, comedy, and an all-you-care-to-enjoy family-style feast.",
    rating: 4.8,
    link: "https://disneyworld.disney.go.com/dining/fort-wilderness-resort/hoop-dee-doo-musical-revue/",
    hours: "Shows at 4:00 PM, 6:15 PM, and 8:30 PM",
    price: "$66 - $74 per adult, $39 - $44 per child (3-9)",
    address: "4510 N Fort Wilderness Trail, Orlando, FL 32830",
    fullDescription: "The Hoop-Dee-Doo Musical Revue is one of the longest-running dinner shows in America, entertaining guests at Disney's Fort Wilderness Resort & Campground since 1974. This lively, family-friendly Wild West musical comedy serves up nostalgic humor, spirited song and dance, and a hearty all-you-care-to-enjoy feast including fried chicken, smoked BBQ ribs, seasonal vegetables, strawberry shortcake, and unlimited beverages including beer, wine, and sangria for guests 21 and older."
  },
  {
    id: 1003,
    name: "Pirates Dinner Adventure",
    category: "Dinner Shows",
    neighborhood: "International Drive",
    image: "/images/dinner-shows/pirates-dinner-adventure.jpeg",
    description: "Set aboard a replicated 18th-century Spanish galleon, this dinner show features swashbuckling pirates performing acrobatics, sword fighting, and comedy.",
    rating: 4.5,
    link: "https://www.piratesdinneradventure.com/",
    hours: "Show times at 5:00 PM & 8:00 PM (hours may vary by day)",
    price: "Adults: $67.95+, Children (3-10): $41.95+",
    address: "6400 Carrier Dr, Orlando, FL 32819",
    fullDescription: "Pirates Dinner Adventure is an immersive dinner theater experience featuring a 46-foot replica Spanish galleon anchored in a 300,000-gallon indoor lagoon. Guests enjoy a three-course meal while watching pirates battle with acrobatics, aerial stunts, swordplay, and dynamic duels. The 90-minute show includes audience participation and special effects. Your dinner includes soup or salad, entree choice (typically chicken, pork, or vegetarian option), vegetables, and dessert."
  },
  {
    id: 1004,
    name: "Sleuths Mystery Dinner Show",
    category: "Dinner Shows",
    neighborhood: "International Drive",
    image: "/images/dinner-shows/sleuths-mystery-dinner-show.jpeg",
    description: "Interactive comedy mystery dinner theater where guests enjoy a three-course meal while solving a murder mystery with the help of professional actors.",
    rating: 4.6,
    link: "https://www.sleuths.com/",
    hours: "Shows typically at 6:00 PM & 8:00 PM (varies by day)",
    price: "Adults: $68.95+, Children (3-11): $29.95+",
    address: "8267 International Dr, Orlando, FL 32819",
    fullDescription: "Sleuths Mystery Dinner Shows offers an interactive dinner theater experience where audience members become detectives to solve a comedic murder mystery. Professional actors lead the investigation among the guests as everyone enjoys a three-course meal with unlimited wine, beer, and soft drinks. The 2.5-hour experience includes a choice of entrées (prime rib, cornish hen, or vegetarian lasagna), plus appetizers and dessert. Each show features different mysteries with unique characters and plots."
  },
  {
    id: 1005,
    name: "Mango's Tropical Cafe",
    category: "Dinner Shows",
    neighborhood: "International Drive",
    image: "/images/dinner-shows/mangos-tropical-cafe.jpeg",
    description: "Vibrant Latin-inspired dinner and nightlife venue featuring multiple themed rooms with live entertainment including dancers, bands, and performers.",
    rating: 4.4,
    link: "https://mangos.com/mangos-orlando/",
    hours: "Open 6:00 PM - 2:00 AM, dinner shows start at 7:00 PM",
    price: "Dinner & Show: $45+ per person, VIP packages available",
    address: "8126 International Dr, Orlando, FL 32819",
    fullDescription: "Mango's Tropical Cafe transforms from a restaurant into a high-energy nightclub with multiple stages featuring spectacular choreographed shows throughout the evening. The venue showcases performers presenting various dance styles including salsa, bachata, reggaeton, and more. Dinner packages include a Cuban/Caribbean-influenced menu with options like churrasco steak, mojo chicken, and seafood. After 10 PM, the venue transitions to a full nightclub experience with DJs and dancing until 2 AM."
  },
  {
    id: 1006,
    name: "The Outta Control Magic Comedy Dinner Show",
    category: "Dinner Shows",
    neighborhood: "International Drive",
    image: "/images/dinner-shows/outta-control-magic-comedy-dinner-show.jpeg",
    description: "Intimate dinner show at WonderWorks combining comedy, magic, impersonations, and audience participation with unlimited pizza, salad, dessert, and drinks. A high-energy family-friendly performance with non-stop entertainment.",
    rating: 4.7,
    link: "https://www.wonderworksonline.com/orlando/the-experience/the-outta-control-magic-comedy-dinner-show/",
    hours: "Shows at 6:00 PM nightly, additional 8:00 PM show on select nights",
    price: "Adults: $31.99+, Children (4-12): $21.99+",
    address: "9067 International Dr, Orlando, FL 32819",
    fullDescription: "The Outta Control Magic Comedy Dinner Show is a family-friendly 90-minute experience featuring magician Tony Brent, who combines comedy, magic, and impersonations with extensive audience participation. Located inside WonderWorks on International Drive, this dinner show offers unlimited pizza, salad, dessert, beer, wine, soda, and more. The intimate venue seats about 125 guests for a close-up magic experience that's consistently ranked as one of Orlando's best entertainment values."
  },

  {
    id: 1008,
    name: "Teatro Martini Orlando",
    category: "Dinner Shows",
    neighborhood: "International Drive",
    image: "/images/dinner-shows/teatro-martini-orlando.jpeg",
    description: "An adult-oriented comedy and variety show featuring hilarious stand-up, burlesque performances, and circus acts, all while enjoying a gourmet four-course meal.",
    rating: 4.7,
    link: "https://teatromartinifl.com",
    hours: "Shows at 7:30 PM on Friday, Saturday at 7:00 PM and 9:30 PM",
    price: "Starting at $54.99 per person, plus tax and gratuity",
    address: "6400 Carrier Dr, Orlando, FL 32819",
    fullDescription: "Teatro Martini Orlando offers an upscale adult dinner show experience with comedy, variety acts, and burlesque performances. Located on International Drive, the show combines stand-up comedy, cirque-style acts, and burlesque dancers for a night of sophisticated adult entertainment. The evening includes a four-course gourmet meal with options like prime rib, salmon, and chicken, along with appetizers and dessert. The intimate cabaret-style setting creates an immersive atmosphere for this 18+ show that blends humor, acrobatics, and dance."
  },
  {
    id: 1009,
    name: "Wantilan Luau",
    category: "Dinner Shows",
    neighborhood: "International Drive",
    image: "/images/dinner-shows/wantilan-luau.jpeg",
    description: "Celebrate at Wantilan Luau at Loews Royal Pacific Resort on International Drive, featuring a tropical buffet and hula dancers at 6300 Hollywood Way. Shows Sat 6 PM, tickets ~$85–$100/adult. A festive Orlando attraction. Book your luau for a Polynesian night!",
    rating: 4.7,
    link: "https://www.universalorlando.com/web/en/us/things-to-do/dining/Wantilan-Luau",
    hours: "Sat 6 PM",
    price: "~$85–$100/adult",
    address: "6300 Hollywood Way, Orlando, FL 32819",
    fullDescription: "Wantilan Luau at Loews Royal Pacific Resort offers guests an authentic Polynesian experience with traditional dance performances, fire knife demonstrations, and a delicious all-you-can-eat island buffet. Every Saturday evening, visitors can enjoy this family-friendly dinner show that celebrates Pacific Island culture with high-energy performances and interactive entertainment. The buffet features Hawaiian specialties including pulled pork, grilled mahi-mahi, and tropical desserts, with complimentary beer, wine, and Mai Tais for guests 21 and over. Located within Universal Orlando Resort, this immersive dining experience provides a perfect taste of island hospitality."
  }
];

export default function AttractionsIndex() {
  // Function to get URL parameters
  const getUrlParam = (paramName: string) => {
    const searchParams = new URLSearchParams(window.location.search);
    return searchParams.get(paramName);
  };
  
  // Determine initial category from URL parameter, localStorage, or default
  const getInitialCategory = () => {
    // First check URL parameter
    const tabParam = getUrlParam('tab');
    if (tabParam) {
      console.log(`Using tab from URL parameter: ${tabParam}`);
      return tabParam;
    }
    
    // Then check localStorage
    const savedTab = localStorage.getItem('lastActiveAttractionsTab');
    if (savedTab) {
      console.log(`Using tab from localStorage: ${savedTab}`);
      return savedTab;
    }
    
    // Default to Unique Attractions
    return "Unique Attractions";
  };
  
  const [selectedCategory, setSelectedCategory] = useState<string>(getInitialCategory());
  const [showMobileFilters, setShowMobileFilters] = useState(false);

  // Get attractions based on selected category
  const getFilteredAttractions = () => {
    switch (selectedCategory) {
      case "Unique Attractions":
        // Return all unique attractions including our hardcoded World of Chocolate
        // Added explicit filters for attractions that should be excluded
        return allUniqueAttractions.filter(a => 
          a.name !== "Disney's Hollywood Studios" &&
          a.name !== "Mango's Tropical Cafe" &&
          a.name !== "The Pinball Palace" &&
          a.name !== "Orlando Auto Museum" &&
          a.name !== "Fun Spot Arcade" &&
          a.name !== "Wantilan Luau" &&
          !a.name.includes("Mango") &&
          !a.name.includes("Tropical Cafe") &&
          !a.name.includes("Pinball Palace") &&
          !a.name.includes("Auto Museum") &&
          !a.name.includes("Fun Spot Arcade") &&
          !a.name.includes("Wantilan Luau")
        );
      case "Outdoor Adventures":
        return outdoorAttractions;
      case "Cultural/Educational":
        return culturalAttractions;
      case "Dinner Shows":
        return dinnerShowAttractions;
      default:
        return allUniqueAttractions.filter(a => a.name !== "Disney's Hollywood Studios");
    }
  };

  const filteredAttractions = getFilteredAttractions();
  
  // Total attractions count
  const totalAttractionsCount = allUniqueAttractions.length + 
                               outdoorAttractions.length + 
                               culturalAttractions.length + 
                               dinnerShowAttractions.length;
  
  // Create category data for the mobile filter component
  const categoryFilterData = [
    {
      id: "Unique Attractions",
      name: "Unique",
      count: allUniqueAttractions.length,
      icon: "🎪",
      color: "bg-amber-500 hover:bg-amber-600"
    },
    {
      id: "Outdoor Adventures",
      name: "Outdoor",
      count: outdoorAttractions.length,
      icon: "🌿",
      color: "bg-green-500 hover:bg-green-600"
    },
    {
      id: "Cultural/Educational",
      name: "Cultural",
      count: culturalAttractions.length,
      icon: "🏛️",
      color: "bg-purple-500 hover:bg-purple-600"
    },
    {
      id: "Dinner Shows",
      name: "Dinner Shows",
      count: dinnerShowAttractions.length,
      icon: "🍽️",
      color: "bg-blue-500 hover:bg-blue-600"
    }
  ];
  
  // Create SEO title based on selected category
  const pageTitle = `${selectedCategory} in Orlando | Awesome Orlando Guide`;
  const pageDescription = `Explore the best ${selectedCategory} in Orlando beyond the major theme parks. Find detailed information, insider tips, and plan your perfect Orlando vacation.`;

  return (
    <div className="container mx-auto px-3 sm:px-4">
      {/* SEO Optimization */}
      <Helmet>
        <title>{pageTitle}</title>
        <meta name="description" content={pageDescription} />
        <meta property="og:title" content={pageTitle} />
        <meta property="og:description" content={pageDescription} />
        <meta property="og:url" content={`${window.location.origin}/attractions`} />
        <meta property="og:type" content="website" />
        <link rel="canonical" href={`${window.location.origin}/attractions`} />
        
        {/* Structured Data for SEO */}
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "CollectionPage",
            "name": pageTitle,
            "description": pageDescription,
            "url": window.location.href,
            "mainEntity": {
              "@type": "ItemList",
              "itemListElement": filteredAttractions.slice(0, 10).map((attraction, index) => ({
                "@type": "ListItem",
                "position": index + 1,
                "item": {
                  "@type": "TouristAttraction",
                  "name": attraction.name,
                  "url": `${window.location.origin}/attraction/${encodeURIComponent(attraction.name)}`,
                  "image": attraction.image
                }
              }))
            }
          })}
        </script>
      </Helmet>

      {/* Fun, Kid-Friendly Hero Section - Compact Version with improved mobile responsiveness */}
      <header className="relative mb-4 sm:mb-6 mt-4 sm:mt-6 overflow-hidden rounded-lg bg-gradient-to-r from-emerald-500 to-teal-500">
        <div className="absolute top-0 left-0 w-full h-full opacity-10">
          <div className="w-full h-full bg-repeat" style={{ 
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.9' fill-rule='evenodd'%3E%3Cpath d='M0 38.59l2.83-2.83 1.41 1.41L1.41 40H0v-1.41zM0 1.4l2.83 2.83 1.41-1.41L1.41 0H0v1.41zM38.59 40l-2.83-2.83 1.41-1.41L40 38.59V40h-1.41zM40 1.41l-2.83 2.83-1.41-1.41L38.59 0H40v1.41zM20 18.6l2.83-2.83 1.41 1.41L21.41 20l2.83 2.83-1.41 1.41L20 21.41l-2.83 2.83-1.41-1.41L18.59 20l-2.83-2.83 1.41-1.41L20 18.59z'/%3E%3C/g%3E%3C/svg%3E")`
          }}></div>
        </div>
        
        <div className="relative z-10 px-3 sm:px-4 py-3 sm:py-4 flex flex-col md:flex-row items-center">
          {/* Title with decorative element - Made more compact */}
          <div className="relative mr-0 md:mr-4">
            <div className="transform rotate-[-2deg] bg-white/10 backdrop-blur-sm px-2 sm:px-3 py-1 rounded-lg border border-teal-200">
              <h1 className="font-display text-xl sm:text-2xl md:text-3xl font-extrabold tracking-tight text-white drop-shadow-[0_1px_2px_rgba(0,0,0,0.3)]">
                ORLANDO<span className="text-yellow-300">ATTRACTIONS</span>
              </h1>
            </div>
            
            {/* Small decorative element */}
            <div className="absolute -top-1 -right-1 w-3 h-3 sm:w-4 sm:h-4 rounded-full bg-orange-300 opacity-80 animate-ping" style={{ animationDuration: '3s' }}></div>
          </div>
          
          {/* Content area - Streamlined */}
          <div className="flex-1 mt-2 md:mt-0">
            <p className="text-[10px] xs:text-xs md:text-sm text-white drop-shadow-md leading-snug mb-1 sm:mb-2 md:mb-1 max-w-md px-1 sm:px-0">
              Discover Orlando's unique attractions beyond the major theme parks.
            </p>
            
            <div className="flex flex-wrap justify-center md:justify-start gap-0.5 sm:gap-1">
              <div className="inline-flex items-center rounded-full bg-teal-700/30 backdrop-blur-sm px-1.5 sm:px-2 py-0.5 text-[9px] xs:text-xs font-medium text-white border border-teal-400/30">
                <MapPin className="mr-0.5 sm:mr-1 h-2 w-2 sm:h-2.5 sm:w-2.5" /> Cultural Sites
              </div>
              <div className="inline-flex items-center rounded-full bg-teal-700/30 backdrop-blur-sm px-1.5 sm:px-2 py-0.5 text-[9px] xs:text-xs font-medium text-white border border-teal-400/30">
                <Sparkles className="mr-0.5 sm:mr-1 h-2 w-2 sm:h-2.5 sm:w-2.5" /> Unique Experiences
              </div>
              <div className="inline-flex items-center rounded-full bg-teal-700/30 backdrop-blur-sm px-1.5 sm:px-2 py-0.5 text-[9px] xs:text-xs font-medium text-white border border-teal-400/30">
                <Info className="mr-0.5 sm:mr-1 h-2 w-2 sm:h-2.5 sm:w-2.5" /> Hidden Gems
              </div>
            </div>
          </div>
        </div>
      </header>
      
      {/* Category filters - moved below the hero */}
      <div className="pb-4 mb-2">
        <AttractionCategoryFilters
          activeCategory={selectedCategory}
          setActiveCategory={setSelectedCategory}
          totalAttractions={totalAttractionsCount}
          categoryData={categoryFilterData}
        />
      </div>
      
      {/* Using Tabs without the TabsList UI since we're using our custom filters instead */}
      <Tabs 
        defaultValue={getInitialCategory()} 
        value={selectedCategory}
        onValueChange={(value) => {
          // Update state with new category
          setSelectedCategory(value);
          
          // Save selected category to localStorage for persistence
          try {
            localStorage.setItem('lastActiveAttractionsTab', value);
            console.log(`Tab changed to: ${value} (saved to localStorage successfully)`);
            
            // For debugging - verify it was saved
            const savedValue = localStorage.getItem('lastActiveAttractionsTab');
            console.log(`Verified localStorage tab value: ${savedValue}`);
          } catch (error) {
            console.error("Error saving tab to localStorage:", error);
          }
        }}
        className="mb-6 md:mb-10"
      >
        
        {/* Attractions Grid - One TabsContent for each category */}
        <TabsContent value="Unique Attractions" className="mt-0">
          <p className="text-gray-500 mb-4 md:mb-6 text-center text-sm md:text-base">
            Showing {allUniqueAttractions.length} attractions in Unique Attractions
          </p>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6">
            {allUniqueAttractions.map((attraction) => (
              <AttractionCard key={attraction.id} attraction={attraction} />
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="Outdoor Adventures" className="mt-0">
          <p className="text-gray-500 mb-4 md:mb-6 text-center text-sm md:text-base">
            Showing {outdoorAttractions.length} attractions in Outdoor Adventures
          </p>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6">
            {outdoorAttractions.map((attraction) => (
              <AttractionCard key={attraction.id} attraction={attraction} />
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="Cultural/Educational" className="mt-0">
          <p className="text-gray-500 mb-4 md:mb-6 text-center text-sm md:text-base">
            Showing {culturalAttractions.length} attractions in 
            <span className="hidden sm:inline"> Cultural/Educational</span>
            <span className="sm:hidden"> Cultural</span>
          </p>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6">
            {culturalAttractions.map((attraction) => (
              <AttractionCard key={attraction.id} attraction={attraction} />
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="Dinner Shows" className="mt-0">
          <p className="text-gray-500 mb-4 md:mb-6 text-center text-sm md:text-base">
            Showing {dinnerShowAttractions.length} dinner shows in Orlando
          </p>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6">
            {dinnerShowAttractions.map((attraction) => (
              <AttractionCard key={attraction.id} attraction={attraction} />
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Import the ShareButton component
import { ShareButton } from '@/components/ShareButton';

// Attraction card component with support for external links
function AttractionCard({ attraction }: { attraction: Attraction }) {
  return (
    <Card className="overflow-hidden h-full flex flex-col transition-all duration-300 hover:shadow-xl hover:-translate-y-1">
      {/* Card Image with Title */}
      <div className="h-40 sm:h-48 overflow-hidden relative">
        <img 
          src={attraction.image} 
          alt={attraction.name}
          className="w-full h-full object-cover hover:scale-105 transition-transform duration-500"
          style={{ 
            objectPosition: 
              attraction.name === "Mango's Tropical Cafe" ? "center top" : 
              attraction.name === "The Outta Control Magic Comedy Dinner Show" ? "center 0%" :
              attraction.name === "Wantilan Luau" ? "center 0%" :
              "center",
            objectFit: "cover",
            width: "100%",
            height: "100%",
            transform: "none"
          }}
        />
        
        {/* Share Button (positioned absolutely over the image) */}
        <ShareButton 
          title={attraction.name}
          url={`${window.location.origin}/attraction/${encodeURIComponent(attraction.name)}`}
          description={`Check out ${attraction.name} in ${attraction.neighborhood || "Orlando"} - ${attraction.description.substring(0, 100)}...`}
          position="absolute"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent">
          <div className="absolute bottom-0 left-0 p-3 sm:p-4">
            <h3 className="text-white font-bold text-base sm:text-lg">{attraction.name}</h3>
            <p className="text-white/80 text-xs sm:text-sm">{attraction.neighborhood}</p>
          </div>
        </div>
      </div>
      
      <CardContent className="p-3 sm:p-4 flex flex-col flex-grow">
        {/* Category and Location Info */}
        <div className="flex items-center flex-wrap gap-2 mb-2 sm:mb-3">
          <Badge className="bg-orange-100 text-orange-800 hover:bg-orange-200 text-xs">
            {attraction.category}
          </Badge>
          <div className="flex items-center text-gray-600 text-xs sm:text-sm">
            <MapPin className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
            <span>{attraction.neighborhood || "Orlando"}</span>
          </div>
        </div>
        
        {/* Map It link - color varies based on category for better visibility */}
        <div className="mb-2">
          <a 
            href={`https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(attraction.address || `${attraction.name} ${attraction.neighborhood || ''} Orlando FL`)}`}
            target="_blank"
            rel="noopener noreferrer"
            className={`inline-flex items-center text-xs font-medium gap-1 hover:underline transition-colors ${
              attraction.category.toLowerCase().includes('dining') ? 'text-orange-600 hover:text-orange-700' :
              attraction.category.toLowerCase().includes('shopping') ? 'text-teal-600 hover:text-teal-700' :
              attraction.category.toLowerCase().includes('cultural') ? 'text-purple-600 hover:text-purple-700' :
              attraction.category.toLowerCase().includes('outdoor') ? 'text-green-600 hover:text-green-700' :
              attraction.category.toLowerCase().includes('nightlife') ? 'text-indigo-600 hover:text-indigo-700' :
              'text-orange-600 hover:text-orange-700' // Default orange for other categories
            }`}
            onClick={(e) => e.stopPropagation()}
          >
            <MapPin className="h-3 w-3" /> Map It
          </a>
        </div>
        
        {/* Description - increased line-clamp to show more text */}
        <p className="text-gray-600 text-xs sm:text-sm line-clamp-3 md:line-clamp-4 mb-auto">{attraction.description}</p>
      </CardContent>
      
      {/* Website and Details Buttons - Now at the very bottom */}
      <div className="flex gap-2 p-3 sm:p-4 pt-0 mt-auto">
        {/* Always show website button or placeholder */}
        {attraction.link ? (
          <a 
            href={attraction.link}
            target="_blank"
            rel="noopener noreferrer"
            className="flex-1 website-btn"
            onClick={(e) => e.stopPropagation()}
          >
            <ExternalLink className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
            <span className="text-xs sm:text-sm">Website</span>
          </a>
        ) : (
          <div className="flex-1 no-website-btn">
            <ExternalLink className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
            <span className="text-xs sm:text-sm">No Website</span>
          </div>
        )}
        <ContextAwareLink
          to={`/attraction/${encodeURIComponent(attraction.name)}`}
          className="flex-1 details-btn"
          preserveScroll={true} // Preserve scroll position when viewing details
        >
          <Info className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
          <span className="text-xs sm:text-sm">Details</span>
        </ContextAwareLink>
      </div>
    </Card>
  );
}