import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'wouter';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  ExternalLink, 
  MapPin, 
  Star, 
  Clock, 
  Users, 
  CalendarDays, 
  Sparkles,
  Ticket,
  Palmtree,
  Droplet,
  Waves,
  Utensils,
  Rocket,
  Baby,
  Gamepad2
} from 'lucide-react';

// Images are directly in the public folder

// Hard-coded theme park data
const themeParkCategories = [
  {
    id: "all",
    name: "All Parks",
  },
  {
    id: "disney",
    name: "Disney",
  },
  {
    id: "universal",
    name: "Universal",
  },
  {
    id: "seaworld",
    name: "SeaWorld & Discovery Cove",
  },
  {
    id: "water-parks",
    name: "Water Parks",
  },
  {
    id: "other",
    name: "Other Attractions",
  }
];

// Theme park interface
interface ThemePark {
  id: string;
  name: string;
  category: string;
  description: string;
  location: string;
  operatingHours: string;
  priceRange: string;
  popularAttractions: string[];
  mainThemes: string[];
  insiderTips: string[];
  websiteUrl: string;
  imageUrl: string;
}

// Hard-coded theme park data
const themeParkData: Record<string, ThemePark> = {
  "volcano-bay": {
    id: "volcano-bay",
    name: "Universal's Volcano Bay",
    category: "water-parks",
    description: "Universal's tropical paradise water theme park featuring the 200-foot Krakatau volcano with immersive water slides, wave pools, and relaxing beaches.",
    location: "Universal Orlando Resort, Orlando, FL",
    operatingHours: "9:00 AM - 7:00 PM (varies seasonally)",
    priceRange: "$80 - $110 per adult",
    popularAttractions: [
      "Krakatau Aqua Coaster",
      "TeAwa The Fearless River",
      "Waturi Beach",
      "Kopiko Wai Winding River",
      "Honu ika Moana"
    ],
    mainThemes: [
      "Polynesian Paradise",
      "Tropical Oasis",
      "Volcanic Adventure",
      "TapuTapu Wearable Technology"
    ],
    insiderTips: [
      "Use the complimentary TapuTapu wearable to hold your place in virtual lines",
      "Arrive early to secure premium seating locations",
      "Consider Premium Seating for shade and service",
      "Take advantage of the late afternoon for shorter waits"
    ],
    websiteUrl: "https://www.universalorlando.com/web/en/us/theme-parks/volcano-bay",
    imageUrl: "/images/volcano-bay.webp"
  },
  "icon-park": {
    id: "icon-park",
    name: "ICON Park",
    category: "other",
    description: "ICON Park is a walkable entertainment complex featuring The Wheel, a 400-foot observation wheel, plus SEA LIFE Aquarium, Madame Tussauds, and over 40 restaurants, bars, and specialty shops.",
    location: "International Drive, Orlando",
    operatingHours: "12:00 PM - 10:00 PM (varies by day)",
    priceRange: "$30-100 for attractions (varies by package)",
    popularAttractions: [
      "The Wheel at ICON Park",
      "SEA LIFE Orlando Aquarium",
      "Madame Tussauds Orlando",
      "Museum of Illusions",
      "Pearl Express Train"
    ],
    mainThemes: [
      "Entertainment Complex",
      "Observation Wheel",
      "Family Attractions",
      "Dining"
    ],
    insiderTips: [
      "Purchase combo tickets for multiple attractions to save money",
      "Visit on weekdays for smaller crowds",
      "Take advantage of the free parking",
      "Check the website for special promotions before visiting",
      "Visit at sunset for the best views from The Wheel"
    ],
    websiteUrl: "https://iconparkorlando.com/",
    imageUrl: "https://iconparkorlando.com/wp-content/uploads/2019/01/Wheel_Night.jpg"
  },
  "kennedy-space-center": {
    id: "kennedy-space-center",
    name: "Kennedy Space Center Visitor Complex",
    category: "other",
    description: "NASA's primary visitor center offering an educational journey through America's space program with authentic spacecraft, engaging exhibits, IMAX films, and even opportunities to meet real astronauts.",
    location: "Merritt Island (about 45 minutes from Orlando)",
    operatingHours: "9:00 AM - 5:00 PM daily",
    priceRange: "$60-75 per person (special experiences additional)",
    popularAttractions: [
      "Space Shuttle Atlantis Exhibit",
      "Shuttle Launch Experience",
      "Apollo/Saturn V Center",
      "Heroes & Legends featuring U.S. Astronaut Hall of Fame",
      "Gateway: The Deep Space Launch Complex"
    ],
    mainThemes: [
      "Space Exploration",
      "NASA History",
      "Educational Exhibits",
      "Interactive Experiences"
    ],
    insiderTips: [
      "Plan a full day visit - there's too much to see in just a few hours",
      "Take the free bus tour to the Apollo/Saturn V Center first thing",
      "Check the launch schedule - you might see a real rocket launch!",
      "Book specialty tours in advance as they often sell out",
      "Bring sunscreen and water as some attractions require outdoor walking"
    ],
    websiteUrl: "https://www.kennedyspacecenter.com/",
    imageUrl: "https://www.kennedyspacecenter.com/-/media/DNC/KSCVC/Attraction-Images/Space-Shuttle-Atlantis/ksc-space-shuttle-atlantis-attraction-16x9.jpg"
  },
  "magic-kingdom": {
    id: "magic-kingdom",
    name: "Magic Kingdom",
    category: "disney",
    description: "The flagship park of Walt Disney World Resort featuring iconic Cinderella Castle and classic attractions organized across six themed lands. Known for its beloved characters, spectacular parades, and nighttime fireworks.",
    location: "Walt Disney World Resort, Lake Buena Vista",
    operatingHours: "9:00 AM - 10:00 PM (varies by season)",
    priceRange: "$109-189 per day (varies by date)",
    popularAttractions: [
      "Space Mountain",
      "Haunted Mansion",
      "Seven Dwarfs Mine Train",
      "Pirates of the Caribbean",
      "Jungle Cruise"
    ],
    mainThemes: [
      "Fantasyland",
      "Tomorrowland",
      "Adventureland",
      "Frontierland",
      "Liberty Square",
      "Main Street U.S.A."
    ],
    insiderTips: [
      "Arrive at rope drop for minimal waits on popular attractions",
      "Use Genie+ and Lightning Lane for high-demand rides",
      "Schedule table-service dining reservations 60 days in advance",
      "Watch the fireworks from Fantasyland for a unique perspective",
      "Take the ferry or resort monorail for arrival to avoid tram lines"
    ],
    websiteUrl: "https://disneyworld.disney.go.com/destinations/magic-kingdom/",
    imageUrl: "https://cdn1.parksmedia.wdprapps.disney.com/resize/mwImage/1/900/360/75/dam/wdpro-assets/parks-and-tickets/destinations/magic-kingdom/magic-kingdom-gallery/cinderella-castle-09-16x9.jpg"
  },
  "epcot": {
    id: "epcot",
    name: "Epcot",
    category: "disney",
    description: "A celebration of human achievement and cultural diversity, featuring futuristic attractions in Future World and international pavilions around World Showcase Lagoon. Known for its seasonal festivals, culinary experiences, and educational focus.",
    location: "Walt Disney World Resort, Lake Buena Vista",
    operatingHours: "10:00 AM - 9:00 PM (varies by season)",
    priceRange: "$109-189 per day (varies by date)",
    popularAttractions: [
      "Guardians of the Galaxy: Cosmic Rewind",
      "Remy's Ratatouille Adventure",
      "Soarin' Around the World",
      "Test Track",
      "Frozen Ever After"
    ],
    mainThemes: [
      "World Discovery",
      "World Celebration",
      "World Nature",
      "World Showcase"
    ],
    insiderTips: [
      "Visit during one of the seasonal festivals for enhanced experiences",
      "Drink/eat around the world in World Showcase countries",
      "Use Single Rider line at Test Track to save time",
      "Watch Harmonious from Italy or Japan pavilions for best views",
      "Book special dining packages for premium fireworks viewing"
    ],
    websiteUrl: "https://disneyworld.disney.go.com/destinations/epcot/",
    imageUrl: "https://cdn1.parksmedia.wdprapps.disney.com/resize/mwImage/1/900/360/75/dam/wdpro-assets/parks-and-tickets/destinations/epcot/epcot-gallery/spaceship-earth-entrance-16x9.jpg"
  },
  "hollywood-studios": {
    id: "hollywood-studios",
    name: "Disney's Hollywood Studios",
    category: "disney",
    description: "A celebration of film, television, music, and theater showcasing the magic of Hollywood. The park features blockbuster attractions based on Star Wars, Toy Story, and more.",
    location: "Walt Disney World Resort, Lake Buena Vista",
    operatingHours: "9:00 AM - 9:00 PM (varies by season)",
    priceRange: "$109-189 per day (varies by date)",
    popularAttractions: [
      "Star Wars: Rise of the Resistance",
      "The Twilight Zone Tower of Terror",
      "Slinky Dog Dash",
      "Mickey & Minnie's Runaway Railway",
      "Rock 'n' Roller Coaster"
    ],
    mainThemes: [
      "Star Wars: Galaxy's Edge",
      "Toy Story Land",
      "Hollywood Boulevard",
      "Sunset Boulevard",
      "Animation Courtyard"
    ],
    insiderTips: [
      "Secure a Boarding Group for Rise of the Resistance at 7am or 1pm",
      "Experience Galaxy's Edge at night for the best atmosphere",
      "Watch Indiana Jones Stunt Spectacular for classic entertainment",
      "Visit Toy Story Land early morning or late evening for fewer crowds",
      "Try Blue or Green Milk in Galaxy's Edge for a unique beverage"
    ],
    websiteUrl: "https://disneyworld.disney.go.com/destinations/hollywood-studios/",
    imageUrl: "https://cdn1.parksmedia.wdprapps.disney.com/resize/mwImage/1/900/360/75/dam/wdpro-assets/parks-and-tickets/attractions/hollywood-studios/star-wars-rise-of-the-resistance/rise-of-the-resistance-hollywood-studios-16x9.jpg"
  },
  "animal-kingdom": {
    id: "animal-kingdom",
    name: "Disney's Animal Kingdom",
    category: "disney",
    description: "A zoological theme park dedicated to natural environment and animal conservation, featuring thousands of live animals in naturalistic habitats. The park blends thrilling attractions with educational exhibits and shows.",
    location: "Walt Disney World Resort, Lake Buena Vista",
    operatingHours: "8:00 AM - 8:00 PM (varies by season)",
    priceRange: "$109-189 per day (varies by date)",
    popularAttractions: [
      "Avatar Flight of Passage",
      "Expedition Everest",
      "Kilimanjaro Safaris",
      "Na'vi River Journey",
      "Festival of the Lion King"
    ],
    mainThemes: [
      "Pandora - The World of Avatar",
      "Africa",
      "Asia",
      "DinoLand U.S.A.",
      "Discovery Island"
    ],
    insiderTips: [
      "Take Kilimanjaro Safaris in early morning for most active animals",
      "Experience Pandora at night when it glows with bioluminescence",
      "Watch Festival of the Lion King for Broadway-quality entertainment",
      "Use single rider line at Expedition Everest for shorter waits",
      "Check the animal trails for close encounters with exotic wildlife"
    ],
    websiteUrl: "https://disneyworld.disney.go.com/destinations/animal-kingdom/",
    imageUrl: "https://cdn1.parksmedia.wdprapps.disney.com/resize/mwImage/1/900/360/75/dam/wdpro-assets/parks-and-tickets/attractions/animal-kingdom/pandora-the-world-of-avatar/pandora-the-world-of-avatar-00.jpg"
  },
  "universal-studios": {
    id: "universal-studios",
    name: "Universal Studios Florida",
    category: "universal",
    description: "A film and TV-based theme park offering behind-the-scenes experiences and immersive attractions based on popular franchises. The park invites guests to ride the movies with cutting-edge simulators and shows.",
    location: "Universal Orlando Resort, Orlando",
    operatingHours: "9:00 AM - 9:00 PM (varies by season)",
    priceRange: "$109-189 per day (varies by date)",
    popularAttractions: [
      "Harry Potter and the Escape from Gringotts",
      "Revenge of the Mummy",
      "Hollywood Rip Ride Rockit",
      "Transformers: The Ride 3D",
      "Men in Black: Alien Attack"
    ],
    mainThemes: [
      "The Wizarding World of Harry Potter - Diagon Alley",
      "New York",
      "San Francisco",
      "Production Central",
      "Springfield (The Simpsons)"
    ],
    insiderTips: [
      "Ride the Hogwarts Express between parks (park-to-park ticket required)",
      "Experience magical interactive wand experiences in Diagon Alley",
      "Watch the daily Celestina Warbeck performance in Diagon Alley",
      "Try Butterbeer in all its varieties (cold, frozen, hot, ice cream)",
      "Stay for the nighttime fountain and projection show"
    ],
    websiteUrl: "https://www.universalorlando.com/web/en/us/theme-parks/universal-studios-florida",
    imageUrl: "https://www.universalorlando.com/webdata/k2/en/us/files/Images/gds/universal-studios-florida-ride-the-movies-b.jpg"
  },
  "islands-of-adventure": {
    id: "islands-of-adventure",
    name: "Universal's Islands of Adventure",
    category: "universal",
    description: "A journey through five islands featuring innovative rides, shows, and attractions. The park combines cutting-edge technology with immersive storytelling for thrill-seekers and families alike.",
    location: "Universal Orlando Resort, Orlando",
    operatingHours: "9:00 AM - 9:00 PM (varies by season)",
    priceRange: "$109-189 per day (varies by date)",
    popularAttractions: [
      "Hagrid's Magical Creatures Motorbike Adventure",
      "The Incredible Hulk Coaster",
      "Jurassic World VelociCoaster",
      "Harry Potter and the Forbidden Journey",
      "The Amazing Adventures of Spider-Man"
    ],
    mainThemes: [
      "The Wizarding World of Harry Potter - Hogsmeade",
      "Marvel Super Hero Island",
      "Jurassic Park",
      "Skull Island",
      "Seuss Landing"
    ],
    insiderTips: [
      "Visit Hogsmeade at night for magical atmosphere with less crowds",
      "Experience the wand selection ceremony at Ollivanders",
      "Watch the Triwizard Spirit Rally and Frog Choir performances",
      "Try all three signature roller coasters for unique experiences",
      "Child swap areas available for families with small children"
    ],
    websiteUrl: "https://www.universalorlando.com/web/en/us/theme-parks/islands-of-adventure",
    imageUrl: "https://www.universalorlando.com/webdata/k2/en/us/files/Images/gds/islands-of-adventure-jurassic-world-velocicoster-b.jpg"
  },
  "epic-universe": {
    id: "epic-universe",
    name: "Universal's Epic Universe",
    category: "universal",
    description: "Opening in 2025, Universal's most ambitious theme park will feature immersive themed worlds including Super Nintendo World, a new Harry Potter land, Universal Monsters, and How to Train Your Dragon. The park promises groundbreaking attractions and technology.",
    location: "Universal Orlando Resort, Orlando",
    operatingHours: "Coming in 2025",
    priceRange: "To be announced",
    popularAttractions: [
      "Super Nintendo World attractions",
      "Ministry of Magic (Harry Potter)",
      "How to Train Your Dragon attractions",
      "Universal Monsters attractions",
      "Universal Celestial Park (central hub)"
    ],
    mainThemes: [
      "Super Nintendo World",
      "The Wizarding World of Harry Potter - Ministry of Magic",
      "How to Train Your Dragon - Isle of Berk",
      "Universal Monsters",
      "Universal Celestial Park"
    ],
    insiderTips: [
      "Park will connect to two Universal resort hotels for early access",
      "Expected to feature unique dining experiences in each land",
      "Park layout designed with a central hub and spoke model",
      "Will include the Dark Universe with classic Universal Monsters",
      "Expected to have the most advanced ride systems in Orlando"
    ],
    websiteUrl: "https://www.universalorlando.com/web/en/us/universal-orlando-resort/the-future",
    imageUrl: "https://www.universalorlando.com/webdata/k2/en/us/files/Images/universal-destination-announcement-epic-universe-header-image-3.jpg"
  },
  "typhoon-lagoon": {
    id: "typhoon-lagoon",
    name: "Disney's Typhoon Lagoon",
    category: "water-parks",
    description: "A tropical paradise water park themed around a typhoon disaster, featuring one of America's largest wave pools, thrilling water slides, and lazy rivers. The park tells the story of a massive storm that created a water-filled paradise.",
    location: "Walt Disney World Resort, Lake Buena Vista",
    operatingHours: "10:00 AM - 5:00 PM (seasonal operation, closed for refurbishment periods)",
    priceRange: "$69-74 per day (varies by date)",
    popularAttractions: [
      "Typhoon Lagoon Surf Pool",
      "Miss Adventure Falls",
      "Crush 'n' Gusher",
      "Castaway Creek",
      "Humunga Kowabunga"
    ],
    mainThemes: [
      "Mount Mayday",
      "Typhoon Lagoon",
      "Keelhaul Falls",
      "Hideaway Bay"
    ],
    insiderTips: [
      "Book surf lessons in the wave pool before park opening",
      "Use the lazy river as transportation around the park",
      "Bring water shoes for hot pavement in summer months",
      "Check for tropical storm closures during hurricane season",
      "Consider a locker rental for valuables"
    ],
    websiteUrl: "https://disneyworld.disney.go.com/destinations/typhoon-lagoon/",
    imageUrl: "https://cdn1.parksmedia.wdprapps.disney.com/resize/mwImage/1/900/360/75/dam/wdpro-assets/parks-and-tickets/attractions/typhoon-lagoon/crush-n-gusher/crush-n-gusher-00.jpg"
  },
  "blizzard-beach": {
    id: "blizzard-beach",
    name: "Disney's Blizzard Beach",
    category: "water-parks",
    description: "A ski resort-themed water park that tells the story of a freak snowstorm in Florida transformed into a water park as the snow melted. Features thrilling water slides and family-friendly attractions with a winter wonderland theme.",
    location: "Walt Disney World Resort, Lake Buena Vista",
    operatingHours: "10:00 AM - 5:00 PM (seasonal operation, closed for refurbishment periods)",
    priceRange: "$69-74 per day (varies by date)",
    popularAttractions: [
      "Summit Plummet",
      "Teamboat Springs",
      "Tike's Peak",
      "Cross Country Creek",
      "Runoff Rapids"
    ],
    mainThemes: [
      "Mount Gushmore",
      "Melt-Away Bay",
      "Ski Patrol Training Camp",
      "Tike's Peak (kids area)"
    ],
    insiderTips: [
      "Experience Summit Plummet early to avoid long wait times",
      "Use the chairlift for unique views and access to multiple slides",
      "Polar Pub offers specialty frozen drinks for adults",
      "Special winter theming during holiday season",
      "Bring a waterproof phone case for unique photo opportunities"
    ],
    websiteUrl: "https://disneyworld.disney.go.com/destinations/blizzard-beach/",
    imageUrl: "https://cdn1.parksmedia.wdprapps.disney.com/resize/mwImage/1/900/360/75/dam/wdpro-assets/parks-and-tickets/attractions/blizzard-beach/summit-plummet/summit-plummet-00.jpg"
  },
  "seaworld": {
    id: "seaworld",
    name: "SeaWorld Orlando",
    category: "seaworld",
    description: "A marine-themed park combining animal attractions, educational exhibits, and thrill rides. SeaWorld showcases marine wildlife with a focus on conservation while offering world-class roller coasters.",
    location: "SeaWorld Orlando, International Drive",
    operatingHours: "9:00 AM - 7:00 PM (varies by season)",
    priceRange: "$89-139 per day (varies by date)",
    popularAttractions: [
      "Manta",
      "Kraken",
      "Journey to Atlantis",
      "Ice Breaker",
      "Orca Encounter"
    ],
    mainThemes: [
      "Sea of Shallows",
      "Sea of Legends",
      "Sea of Ice",
      "Sea of Delight",
      "Sea of Power"
    ],
    insiderTips: [
      "Plan around show schedules for orcas and dolphins",
      "Visit Shark Encounter for an underwater viewing tunnel",
      "Feed stingrays at Stingray Lagoon (additional fee)",
      "Bring a poncho for water rides and splash zones",
      "Look for animal ambassadors in educational areas"
    ],
    websiteUrl: "https://seaworld.com/orlando/",
    imageUrl: "https://seaworld.com/orlando/-/media/seaworld-orlando/images/tickets/tickets-flex-3-park-shared/swf_buy1get1_freebttbat_mpu_1280x400.ashx"
  },
  "disney-springs": {
    id: "disney-springs",
    name: "Disney Springs",
    category: "disney",
    description: "Disney Springs is Walt Disney World's premier shopping, dining, and entertainment district featuring over 150 different venues across four distinct neighborhoods: The Landing, Town Center, Marketplace, and West Side.",
    location: "Walt Disney World Resort, Lake Buena Vista",
    operatingHours: "10:00 AM - 12:00 AM (varies by venue)",
    priceRange: "Free admission (pay for shopping and dining)",
    popularAttractions: [
      "The BOATHOUSE restaurant",
      "World of Disney store",
      "Cirque du Soleil - Drawn to Life",
      "AMC Disney Springs 24",
      "House of Blues"
    ],
    mainThemes: [
      "Dining & Restaurants",
      "Shopping & Retail",
      "Entertainment",
      "Waterfront Views",
      "Disney Merchandise"
    ],
    insiderTips: [
      "Visit on weekdays for smaller crowds",
      "Free parking in all Disney Springs garages",
      "Make dining reservations well in advance for popular restaurants",
      "Check the event calendar for live entertainment",
      "Use the dedicated Disney Springs bus from Disney resorts"
    ],
    websiteUrl: "https://www.disneysprings.com",
    imageUrl: "/Disney Springs.jpg"
  },
  "discovery-cove": {
    id: "discovery-cove",
    name: "Discovery Cove",
    category: "seaworld",
    description: "An all-inclusive day resort experience offering intimate animal encounters in a tropical setting. The exclusive park limits daily attendance for an uncrowded experience with dolphin swims, snorkeling, and relaxation.",
    location: "Discovery Cove, International Drive area",
    operatingHours: "8:00 AM - 5:00 PM (reservation required)",
    priceRange: "$149-249 per day (depending on package)",
    popularAttractions: [
      "Dolphin Lagoon",
      "Grand Reef",
      "Serenity Bay",
      "Explorer's Aviary",
      "Wind-Away River"
    ],
    mainThemes: [
      "Tropical Reef",
      "Freshwater Oasis",
      "The Grand Reef",
      "Dolphin Lagoon"
    ],
    insiderTips: [
      "Book well in advance for dolphin swim experiences",
      "All food, drinks, equipment, and parking included in admission",
      "Bring a waterproof camera or purchase photo package",
      "Packages include unlimited admission to SeaWorld/Aquatica",
      "Morning time slots for dolphin swims offer best lighting for photos"
    ],
    websiteUrl: "https://discoverycove.com/orlando/",
    imageUrl: "/images/discovery-cove.jpg"
  },
  "aquatica": {
    id: "aquatica",
    name: "Aquatica Orlando",
    category: "water-parks",
    description: "SeaWorld's water park combines thrilling water slides with up-close animal experiences in a South Seas-inspired setting. The park features unique marine life encounters alongside traditional water park attractions.",
    location: "Aquatica Orlando, International Drive area",
    operatingHours: "10:00 AM - 5:00 PM (seasonal hours)",
    priceRange: "$69-99 per day (varies by date)",
    popularAttractions: [
      "Dolphin Plunge",
      "Ihu's Breakaway Falls",
      "Roa's Rapids",
      "Cutback Cove",
      "Whanau Way"
    ],
    mainThemes: [
      "Walkabout Waters",
      "Loggerhead Lane",
      "Kata's Kookaburra Cove",
      "Commerson's Dolphin habitat"
    ],
    insiderTips: [
      "Experience Dolphin Plunge to slide through dolphin habitat",
      "Rent a premium cabana for a shaded home base",
      "Check height requirements before waiting in line",
      "Visit on weekdays for shorter wait times",
      "Purchase Quick Queue for popular slides during peak season"
    ],
    websiteUrl: "https://aquatica.com/orlando/",
    imageUrl: "https://aquatica.com/san-antonio/-/media/aquatica-san-antonio/images/tickets/annual-pass/aquatica-annual-passes-mpu-1280x400.ashx"
  },
  "legoland": {
    id: "legoland",
    name: "LEGOLAND Florida",
    category: "other",
    description: "A family-focused theme park dedicated to the LEGO brand with over 50 rides, shows, and attractions. The park encourages creativity and imagination with interactive experiences designed primarily for children 2-12.",
    location: "Winter Haven (45 min from Orlando)",
    operatingHours: "10:00 AM - 6:00 PM (varies by season)",
    priceRange: "$89-119 per day (varies by date)",
    popularAttractions: [
      "LEGO NINJAGO World",
      "Miniland USA",
      "LEGO City",
      "LEGOLAND Water Park",
      "Cypress Gardens"
    ],
    mainThemes: [
      "LEGO City",
      "LEGO Technic",
      "LEGO Pirates",
      "LEGO Kingdoms",
      "DUPLO Valley"
    ],
    insiderTips: [
      "Arrive early to avoid afternoon crowds",
      "Visit the original Cypress Gardens area for botanical beauty",
      "Check show schedules for water ski performances",
      "Bring your own LEGO minifigure to trade with staff",
      "Consider the water park add-on during hot months"
    ],
    websiteUrl: "https://www.legoland.com/florida/",
    imageUrl: "https://www.legoland.com/florida/sites/florida/files/2023-11/23%20PEPPA%20PIG%20THEME%20PARK%20MUDDY%20PUDDLES.jpg"
  },
  "fun-spot-america": {
    id: "fun-spot-america",
    name: "Fun Spot America",
    category: "other",
    description: "A family-owned amusement park with locations in Orlando and Kissimmee offering multi-level go-kart tracks and traditional amusement rides at a fraction of major theme park prices. Known for its relaxed atmosphere and no-line policy.",
    location: "International Drive and Kissimmee (separate locations)",
    operatingHours: "10:00 AM - 12:00 AM (varies by location)",
    priceRange: "$54.95 for all-day armband",
    popularAttractions: [
      "White Lightning (wooden coaster)",
      "Freedom Flyer (steel coaster)",
      "Multi-level Go-Karts",
      "Skycoaster",
      "Ferris Wheel"
    ],
    mainThemes: [
      "Thrill Rides",
      "Family Rides",
      "Kid Rides",
      "Go-Karts",
      "Arcade Games"
    ],
    insiderTips: [
      "Purchase all-day armbands for unlimited rides",
      "Visit at night for cooler temperatures and lighted rides",
      "Check for discount coupons online before visiting",
      "Experience White Lightning wooden coaster in Orlando location",
      "Consider the Skycoaster upcharge attraction for extreme thrills"
    ],
    websiteUrl: "https://fun-spot.com/",
    imageUrl: "https://fun-spot.com/wp-content/uploads/2023/08/White-Lightning-orange-track-fun-spot-america-1536x1024.jpg"
  },
  "gatorland": {
    id: "gatorland",
    name: "Gatorland",
    category: "other",
    description: "A unique wildlife preserve and theme park showcasing thousands of alligators and crocodiles. The 'Alligator Capital of the World' offers animal shows, breeding marsh, and adventure activities in a natural setting.",
    location: "South Orange Blossom Trail, Orlando",
    operatingHours: "10:00 AM - 5:00 PM daily",
    priceRange: "$32.99 adults, $22.99 children (3-12)",
    popularAttractions: [
      "Breeding Marsh",
      "Screamin' Gator Zip Line",
      "Gator Jumparoo Show",
      "Gator Wrestling Show",
      "Alligator Breeding Marsh Observation Tower"
    ],
    mainThemes: [
      "Alligator Exhibits",
      "Florida Wildlife",
      "Adventure Activities",
      "Educational Shows",
      "Petting Zoo"
    ],
    insiderTips: [
      "Take the Screamin' Gator Zip Line for a unique view",
      "Don't miss the iconic giant gator mouth entrance for photos",
      "Visit during gator feeding times for most active animals",
      "Schedule around the live shows throughout the day",
      "Book an adventure tour for behind-the-scenes experiences"
    ],
    websiteUrl: "https://www.gatorland.com/",
    imageUrl: "https://www.gatorland.com/wp-content/uploads/2019/10/gatorland-park-hours-header.jpg"
  }
};

function ThemeParkExperienceHardCoded() {
  // State for the selected theme park category filter
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [location] = useLocation();
  
  // Scroll to top when the page loads
  useEffect(() => {
    window.scrollTo(0, 0);
  }, [location]);
  
  // Filtered theme parks based on selected category
  const filteredThemeParks = selectedCategory === "all"
    ? Object.values(themeParkData)
    : Object.values(themeParkData).filter(park => park.category === selectedCategory);

  // Handle category change
  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
  };

  // Get icon based on category
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "disney":
        return <Sparkles className="w-4 h-4" />;
      case "universal":
        return <Palmtree className="w-4 h-4" />;
      case "seaworld":
        return <Droplet className="w-4 h-4" />;
      case "water-parks":
        return <Waves className="w-4 h-4" />;
      default:
        return <Star className="w-4 h-4" />;
    }
  };

  return (
    <div className="container mx-auto px-4">
      {/* Ultra-Compact Fun, Kid-Friendly Hero Section with matching mt-6 gap */}
      <header className="relative mb-4 sm:mb-6 mt-4 sm:mt-6 overflow-hidden rounded-lg bg-gradient-to-r from-blue-600 via-purple-500 to-pink-500">
        <div className="absolute top-0 left-0 w-full h-full opacity-10">
          <div className="w-full h-full bg-repeat" style={{ 
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M30 30c-5.523 0-10-4.477-10-10s4.477-10 10-10 10 4.477 10 10-4.477 10-10 10zm20 10c-5.523 0-10-4.477-10-10s4.477-10 10-10 10 4.477 10 10-4.477 10-10 10zm-20 0c-5.523 0-10-4.477-10-10s4.477-10 10-10 10 4.477 10 10-4.477 10-10 10zM10 40c-5.523 0-10-4.477-10-10s4.477-10 10-10 10 4.477 10 10-4.477 10-10 10zm0-20C4.477 20 0 15.523 0 10S4.477 0 10 0s10 4.477 10 10-4.477 10-10 10zm20 0c-5.523 0-10-4.477-10-10S24.477 0 30 0s10 4.477 10 10-4.477 10-10 10zm20 0c-5.523 0-10-4.477-10-10S44.477 0 50 0s10 4.477 10 10-4.477 10-10 10z' fill='%23ffffff' fill-opacity='1' fill-rule='evenodd'/%3E%3C/svg%3E")`
          }}></div>
        </div>
        
        <div className="relative z-10 px-3 sm:px-4 py-3 sm:py-4 flex flex-col md:flex-row items-center">
          {/* Title with decorative element - Made more compact */}
          <div className="relative mr-0 md:mr-4">
            <div className="transform rotate-[-2deg] bg-white/10 backdrop-blur-sm px-2 sm:px-3 py-1 rounded-lg border border-blue-200">
              <h1 className="font-display text-xl sm:text-2xl md:text-3xl font-extrabold tracking-tight text-white drop-shadow-[0_1px_2px_rgba(0,0,0,0.3)]">
                ORLANDO<span className="text-yellow-300">THEME PARKS</span>
              </h1>
            </div>
            
            {/* Small decorative element */}
            <div className="absolute -top-1 -right-1 w-3 h-3 sm:w-4 sm:h-4 rounded-full bg-yellow-300 opacity-80 animate-ping" style={{ animationDuration: '3s' }}></div>
          </div>
          
          {/* Content area - Streamlined */}
          <div className="flex-1 mt-2 md:mt-0">
            <p className="text-[10px] xs:text-xs md:text-sm text-white drop-shadow-md leading-snug mb-1 sm:mb-2 md:mb-1 px-1 sm:px-0">
              Discover the magic of world-class theme parks that make Orlando the Theme Park Capital of the World.
            </p>
            
            <div className="flex flex-wrap justify-center md:justify-start gap-0.5 sm:gap-1">
              <div className="inline-flex items-center rounded-full bg-purple-700/30 backdrop-blur-sm px-1.5 sm:px-2 py-0.5 text-[9px] xs:text-xs font-medium text-white border border-purple-400/30">
                <Rocket className="mr-0.5 sm:mr-1 h-2 w-2 sm:h-2.5 sm:w-2.5" /> Thrilling Rides
              </div>
              <div className="inline-flex items-center rounded-full bg-purple-700/30 backdrop-blur-sm px-1.5 sm:px-2 py-0.5 text-[9px] xs:text-xs font-medium text-white border border-purple-400/30">
                <Sparkles className="mr-0.5 sm:mr-1 h-2 w-2 sm:h-2.5 sm:w-2.5" /> Magical Experiences
              </div>
              <div className="inline-flex items-center rounded-full bg-purple-700/30 backdrop-blur-sm px-1.5 sm:px-2 py-0.5 text-[9px] xs:text-xs font-medium text-white border border-purple-400/30">
                <Palmtree className="mr-0.5 sm:mr-1 h-2 w-2 sm:h-2.5 sm:w-2.5" /> Adventures
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main theme park experiences - Disney and Universal */}
      <section className="mb-16">
        
        {/* Disney World Resort */}
        <div className="mb-12">
          <div className="flex items-center mb-6">
            <div className="w-10 h-10 rounded-full bg-blue-600 flex items-center justify-center mr-3">
              <Sparkles className="h-5 w-5 text-white" />
            </div>
            <h3 className="text-2xl md:text-3xl font-extrabold text-blue-700">Walt Disney World Resort</h3>
          </div>
          <div className="space-y-6">
            {/* Disney World Main Entry Door */}
            <div className="group relative rounded-xl overflow-hidden shadow-xl transition-all duration-500 hover:shadow-2xl border-2 border-blue-200 hover:border-blue-400">
              <div className="absolute inset-0 bg-gradient-to-b from-blue-600/40 to-purple-900/80 mix-blend-multiply transition-opacity group-hover:opacity-70 z-10"></div>
              <div className="w-full h-[400px] overflow-hidden" style={{ position: 'relative' }}>
                <video 
                  style={{ 
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    objectFit: 'fill',
                    objectPosition: 'center center',
                    transform: 'scale(1.45)' /* Increased from 1.35 to 1.45 (45% total stretch) */
                  }}
                  autoPlay 
                  loop 
                  muted 
                  playsInline
                >
                  <source src="/castle-video.mp4" type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
              </div>
              <div className="absolute inset-0 flex flex-col justify-end p-8 z-20">
                <h3 className="text-3xl md:text-4xl font-bold text-white mb-2 drop-shadow-lg">Walt Disney World</h3>
                <p className="text-white text-lg mb-6 drop-shadow-md max-w-lg">
                  Experience the magic of four iconic theme parks, two water parks, and Disney Springs in the most magical place on earth.
                </p>
                <a href="https://disneyworld.disney.go.com/" target="_blank" rel="noopener noreferrer">
                  <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-lg gap-2 shadow-lg transform transition hover:translate-y-[-2px]">
                    <Sparkles className="h-5 w-5" /> Official Site
                  </Button>
                </a>
              </div>
            </div>
            
            {/* Disney Parks Montage - Interactive Photo Gallery with Links */}
            <div className="mb-8 overflow-hidden">
              <h3 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                <Sparkles className="h-5 w-5 mr-2 text-blue-600" /> 
                <span>Visit the Magic of Disney Parks</span>
              </h3>
              
              <div className="grid grid-cols-12 gap-3">
                {/* Row 1 - Top Row */}
                <div className="grid grid-cols-12 col-span-12 gap-3 h-[200px]">
                  {/* Magic Kingdom */}
                  <a href="https://disneyworld.disney.go.com/destinations/magic-kingdom/" target="_blank" rel="noopener noreferrer" 
                     className="col-span-6 md:col-span-4 relative rounded-xl overflow-hidden shadow-lg transition-all duration-500 group hover:-translate-y-1 hover:shadow-xl">
                    <div className="absolute inset-0 bg-gradient-to-b from-pink-600/30 to-purple-900/70 mix-blend-multiply transition-opacity group-hover:opacity-40 z-10"></div>
                    <div className="w-full h-full overflow-hidden">
                      <img 
                        src="/Donald Duck.jpeg"
                        alt="Magic Kingdom - Donald Duck" 
                        className="w-full h-[120%] object-cover object-[center_top] translate-y-[-40px] scale-y-[1.1] transition-transform duration-700 group-hover:scale-105"
                      />
                    </div>
                    <div className="absolute inset-0 flex items-center justify-center z-20">
                      <h4 className="text-lg md:text-xl font-bold text-white drop-shadow-lg bg-black/20 backdrop-blur-sm rounded-full px-4 py-2">Magic Kingdom</h4>
                    </div>
                    <div className="absolute bottom-2 right-2 z-20 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <span className="text-xs text-white bg-blue-600 rounded-full px-2 py-1 flex items-center">
                        <ExternalLink className="h-3 w-3 mr-1" /> Official Site
                      </span>
                    </div>
                  </a>
                  
                  {/* EPCOT */}
                  <a href="https://disneyworld.disney.go.com/destinations/epcot/" 
                     target="_blank" rel="noopener noreferrer" 
                     className="col-span-6 md:col-span-4 relative rounded-xl overflow-hidden shadow-lg transition-all duration-500 group hover:-translate-y-1 hover:shadow-xl">
                    <div className="absolute inset-0 bg-gradient-to-b from-blue-600/30 to-blue-900/70 mix-blend-multiply transition-opacity group-hover:opacity-40 z-10"></div>
                    <img 
                      src="/epcot.webp"
                      alt="Epcot" 
                      className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 flex items-center justify-center z-20">
                      <h4 className="text-lg md:text-xl font-bold text-white drop-shadow-lg bg-black/20 backdrop-blur-sm rounded-full px-4 py-2">EPCOT</h4>
                    </div>
                    <div className="absolute bottom-2 right-2 z-20 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <span className="text-xs text-white bg-blue-600 rounded-full px-2 py-1 flex items-center">
                        <ExternalLink className="h-3 w-3 mr-1" /> Official Site
                      </span>
                    </div>
                  </a>
                  
                  {/* Hollywood Studios */}
                  <a href="https://disneyworld.disney.go.com/destinations/hollywood-studios/" 
                     target="_blank" rel="noopener noreferrer" 
                     className="col-span-12 md:col-span-4 relative rounded-xl overflow-hidden shadow-lg transition-all duration-500 group hover:-translate-y-1 hover:shadow-xl">
                    <div className="absolute inset-0 bg-gradient-to-b from-red-600/30 to-orange-900/70 mix-blend-multiply transition-opacity group-hover:opacity-40 z-10"></div>
                    <img 
                      src="/Hollywood Studios.jpeg"
                      alt="Hollywood Studios" 
                      className="w-full h-[130%] object-cover object-top translate-y-[-60px] scale-y-[1.1] transition-transform duration-700 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 flex items-center justify-center z-20">
                      <h4 className="text-lg md:text-xl font-bold text-white drop-shadow-lg bg-black/20 backdrop-blur-sm rounded-full px-4 py-2">Hollywood Studios</h4>
                    </div>
                    <div className="absolute bottom-2 right-2 z-20 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <span className="text-xs text-white bg-blue-600 rounded-full px-2 py-1 flex items-center">
                        <ExternalLink className="h-3 w-3 mr-1" /> Official Site
                      </span>
                    </div>
                  </a>
                </div>
                
                {/* Row 2 - Bottom Row */}
                <div className="grid grid-cols-12 col-span-12 gap-3 h-[200px] mt-3">
                  {/* Animal Kingdom */}
                  <a href="https://disneyworld.disney.go.com/destinations/animal-kingdom/" 
                     target="_blank" rel="noopener noreferrer" 
                     className="col-span-12 md:col-span-4 relative rounded-xl overflow-hidden shadow-lg transition-all duration-500 group hover:-translate-y-1 hover:shadow-xl">
                    <div className="absolute inset-0 bg-gradient-to-b from-green-600/20 to-green-900/60 mix-blend-multiply transition-opacity group-hover:opacity-40 z-10"></div>
                    <img 
                      src="/Animal Kingdom.jpg"
                      alt="Disney's Animal Kingdom" 
                      className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 flex items-center justify-center z-20">
                      <h4 className="text-lg md:text-xl font-bold text-white drop-shadow-lg bg-black/20 backdrop-blur-sm rounded-full px-4 py-2">Animal Kingdom</h4>
                    </div>
                    <div className="absolute bottom-2 right-2 z-20 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <span className="text-xs text-white bg-blue-600 rounded-full px-2 py-1 flex items-center">
                        <ExternalLink className="h-3 w-3 mr-1" /> Official Site
                      </span>
                    </div>
                  </a>
                  
                  {/* Disney Springs */}
                  <a href="https://www.disneysprings.com" 
                     target="_blank" rel="noopener noreferrer" 
                     className="col-span-6 md:col-span-3 relative rounded-xl overflow-hidden shadow-lg transition-all duration-500 group hover:-translate-y-1 hover:shadow-xl">
                    <div className="absolute inset-0 bg-gradient-to-b from-yellow-600/30 to-orange-900/70 mix-blend-multiply transition-opacity group-hover:opacity-40 z-10"></div>
                    <img 
                      src="/Disney Springs.jpg"
                      alt="Disney Springs" 
                      className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 flex items-center justify-center z-20">
                      <h4 className="text-lg md:text-xl font-bold text-white drop-shadow-lg bg-black/20 backdrop-blur-sm rounded-full px-4 py-2">Disney Springs</h4>
                    </div>
                    <div className="absolute bottom-2 right-2 z-20 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <span className="text-xs text-white bg-blue-600 rounded-full px-2 py-1 flex items-center">
                        <ExternalLink className="h-3 w-3 mr-1" /> Official Site
                      </span>
                    </div>
                  </a>
                  
                  {/* World of Avatar */}
                  <a href="https://disneyworld.disney.go.com/destinations/animal-kingdom/pandora-world-of-avatar/" 
                     target="_blank" rel="noopener noreferrer" 
                     className="col-span-6 md:col-span-3 relative rounded-xl overflow-hidden shadow-lg transition-all duration-500 group hover:-translate-y-1 hover:shadow-xl">
                    <div className="absolute inset-0 bg-gradient-to-b from-cyan-600/30 to-blue-900/70 mix-blend-multiply transition-opacity group-hover:opacity-40 z-10"></div>
                    <img 
                      src="/Avatar.webp"
                      alt="Pandora - The World of Avatar" 
                      className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 flex items-center justify-center z-20">
                      <h4 className="text-md md:text-lg font-bold text-white drop-shadow-lg bg-black/20 backdrop-blur-sm rounded-full px-4 py-2">Pandora</h4>
                    </div>
                    <div className="absolute bottom-2 right-2 z-20 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <span className="text-xs text-white bg-blue-600 rounded-full px-2 py-1 flex items-center">
                        <ExternalLink className="h-3 w-3 mr-1" /> Official Site
                      </span>
                    </div>
                  </a>
                  
                  {/* Galaxy's Edge */}
                  <a href="https://disneyworld.disney.go.com/destinations/hollywood-studios/star-wars-galaxys-edge/" 
                     target="_blank" rel="noopener noreferrer" 
                     className="col-span-12 md:col-span-2 relative rounded-xl overflow-hidden shadow-lg transition-all duration-500 group hover:-translate-y-1 hover:shadow-xl">
                    <div className="absolute inset-0 bg-gradient-to-b from-gray-600/30 to-gray-900/70 mix-blend-multiply transition-opacity group-hover:opacity-40 z-10"></div>
                    <img 
                      src="/Star Wars.webp"
                      alt="Star Wars: Galaxy's Edge" 
                      className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 flex items-center justify-center z-20">
                      <h4 className="text-md md:text-lg font-bold text-white drop-shadow-lg bg-black/20 backdrop-blur-sm rounded-full px-4 py-2">Galaxy's Edge</h4>
                    </div>
                    <div className="absolute bottom-2 right-2 z-20 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <span className="text-xs text-white bg-blue-600 rounded-full px-2 py-1 flex items-center">
                        <ExternalLink className="h-3 w-3 mr-1" /> Official Site
                      </span>
                    </div>
                  </a>
                </div>
              </div>
            </div>
            
            {/* Disney Water Parks */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Typhoon Lagoon */}
              <div className="group relative rounded-xl overflow-hidden shadow-md transition-all duration-500 hover:shadow-lg border border-blue-100 hover:border-blue-300">
                <div className="absolute inset-0 bg-gradient-to-b from-blue-600/30 to-blue-900/70 mix-blend-multiply transition-opacity group-hover:opacity-70 z-10"></div>
                <img
                  src="/Typhoon Lagoon.jpg"
                  alt="Disney's Typhoon Lagoon"
                  className="w-full h-[180px] object-cover transition-transform duration-700 group-hover:scale-110"
                />
                <div className="absolute inset-0 flex flex-col justify-end p-4 z-20">
                  <h3 className="text-xl font-bold text-white mb-1 drop-shadow-lg">Disney's Typhoon Lagoon</h3>
                  <p className="text-white text-sm mb-3 drop-shadow-md max-w-lg line-clamp-2">
                    Tropical paradise water park featuring one of America's largest wave pools, thrilling water slides, and lazy river.
                  </p>
                  <a href={themeParkData["typhoon-lagoon"].websiteUrl} target="_blank" rel="noopener noreferrer">
                    <Button size="sm" className="bg-blue-600 hover:bg-blue-700 gap-1 shadow-lg transform transition hover:translate-y-[-2px]">
                      <Waves className="h-3 w-3" /> Official Site
                    </Button>
                  </a>
                </div>
              </div>
              
              {/* Blizzard Beach */}
              <div className="group relative rounded-xl overflow-hidden shadow-md transition-all duration-500 hover:shadow-lg border border-blue-100 hover:border-blue-300">
                <div className="absolute inset-0 bg-gradient-to-b from-blue-600/30 to-blue-900/70 mix-blend-multiply transition-opacity group-hover:opacity-70 z-10"></div>
                <img
                  src="/Blizzard Beach.jpg"
                  alt="Disney's Blizzard Beach"
                  className="w-full h-[180px] object-cover transition-transform duration-700 group-hover:scale-110"
                />
                <div className="absolute inset-0 flex flex-col justify-end p-4 z-20">
                  <h3 className="text-xl font-bold text-white mb-1 drop-shadow-lg">Disney's Blizzard Beach</h3>
                  <p className="text-white text-sm mb-3 drop-shadow-md max-w-lg line-clamp-2">
                    Ski resort themed water park featuring Summit Plummet, one of the tallest and fastest water slides in the world.
                  </p>
                  <a href={themeParkData["blizzard-beach"].websiteUrl} target="_blank" rel="noopener noreferrer">
                    <Button size="sm" className="bg-blue-600 hover:bg-blue-700 gap-1 shadow-lg transform transition hover:translate-y-[-2px]">
                      <Waves className="h-3 w-3" /> Official Site
                    </Button>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Universal Orlando Resort */}
        <div className="mb-12">
          <div className="flex items-center mb-6">
            <div className="w-10 h-10 rounded-full bg-gradient-to-r from-red-600 to-blue-600 flex items-center justify-center mr-3">
              <Rocket className="h-5 w-5 text-white" />
            </div>
            <h3 className="text-2xl md:text-3xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-red-600 to-blue-600">Universal Orlando Resort</h3>
          </div>
          <div className="space-y-6">
            {/* Universal Main Entry Door */}
            <div className="group relative rounded-xl overflow-hidden shadow-xl transition-all duration-500 hover:shadow-2xl border-2 border-red-200 hover:border-red-400">
              <div className="absolute inset-0 bg-gradient-to-b from-red-600/40 to-blue-900/80 mix-blend-multiply transition-opacity group-hover:opacity-70 z-10"></div>
              <img
                src="/Universal Entrance.webp"
                alt="Universal Orlando Resort Entrance"
                className="w-full h-[400px] object-cover transition-transform duration-700 group-hover:scale-110"
              />
              <div className="absolute top-0 left-0 right-0 p-5 z-20 flex justify-between items-start">

              </div>
              <div className="absolute inset-0 flex flex-col justify-end p-8 z-20">
                <h3 className="text-3xl md:text-4xl font-bold text-white mb-2 drop-shadow-lg">Universal Orlando Resort</h3>
                <p className="text-white text-lg mb-6 drop-shadow-md max-w-lg">
                  Immerse yourself in thrilling adventures across Universal Studios, Islands of Adventure, and the upcoming Epic Universe.
                </p>
                <div className="flex flex-wrap gap-4">
                  <a href="https://www.universalorlando.com/" target="_blank" rel="noopener noreferrer">
                    <Button size="lg" className="bg-gradient-to-r from-red-600 to-blue-600 hover:from-red-700 hover:to-blue-700 text-lg gap-2 shadow-lg transform transition hover:translate-y-[-2px]">
                      <Rocket className="h-5 w-5" /> Official Site
                    </Button>
                  </a>
                </div>
              </div>
            </div>
            
            {/* Universal Parks Gallery */}
            <div className="my-8">
              <h4 className="text-lg font-medium text-blue-800 mb-3">Explore Universal's Incredible Theme Parks</h4>
              
              <div className="bg-gradient-to-br from-red-50 to-blue-50 rounded-xl p-4 shadow-md">
                <div className="grid grid-cols-12 col-span-12 gap-3 h-[200px]">
                  {/* Universal Studios Florida */}
                  <a href="https://www.universalorlando.com/web/en/us/theme-parks/universal-studios-florida" 
                     target="_blank" rel="noopener noreferrer" 
                     className="col-span-12 md:col-span-6 relative rounded-xl overflow-hidden shadow-lg transition-all duration-500 group hover:-translate-y-1 hover:shadow-xl">
                    <div className="absolute inset-0 bg-gradient-to-b from-red-600/30 to-purple-900/70 mix-blend-multiply transition-opacity group-hover:opacity-40 z-10"></div>
                    <img 
                      src="/Universal Studios Florida.jpeg"
                      alt="Universal Studios Florida" 
                      className="w-full h-full object-cover object-center transition-transform duration-700 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 flex items-center justify-center z-20">
                      <h4 className="text-lg md:text-xl font-bold text-white drop-shadow-lg bg-black/20 backdrop-blur-sm rounded-full px-4 py-2">Universal Studios Florida</h4>
                    </div>
                    <div className="absolute bottom-2 right-2 z-20 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <span className="text-xs text-white bg-blue-600 rounded-full px-2 py-1 flex items-center">
                        <ExternalLink className="h-3 w-3 mr-1" /> Official Site
                      </span>
                    </div>
                  </a>
                  
                  {/* Islands of Adventure */}
                  <a href="https://www.universalorlando.com/web/en/us/theme-parks/islands-of-adventure" 
                     target="_blank" rel="noopener noreferrer" 
                     className="col-span-12 md:col-span-6 relative rounded-xl overflow-hidden shadow-lg transition-all duration-500 group hover:-translate-y-1 hover:shadow-xl">
                    <div className="absolute inset-0 bg-gradient-to-b from-green-600/30 to-blue-900/70 mix-blend-multiply transition-opacity group-hover:opacity-40 z-10"></div>
                    <img 
                      src="/Islands of Adventure.jpeg"
                      alt="Islands of Adventure" 
                      className="w-full h-[120%] object-cover object-[center_top] translate-y-[-20px] transition-transform duration-700 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 flex items-center justify-center z-20">
                      <h4 className="text-lg md:text-xl font-bold text-white drop-shadow-lg bg-black/20 backdrop-blur-sm rounded-full px-4 py-2">Islands of Adventure</h4>
                    </div>
                    <div className="absolute bottom-2 right-2 z-20 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <span className="text-xs text-white bg-blue-600 rounded-full px-2 py-1 flex items-center">
                        <ExternalLink className="h-3 w-3 mr-1" /> Official Site
                      </span>
                    </div>
                  </a>
                </div>
              </div>
            </div>

            {/* Epic Universe Montage */}
            <div className="my-8">
              <h4 className="text-lg font-medium text-purple-800 mb-3">Experience Universal's Epic Universe - Now Open</h4>
              
              {/* Epic Universe Entrance Video - Mobile Optimized */}
              <div className="rounded-xl overflow-hidden shadow-lg mb-4 relative">
                <div className="absolute inset-0 bg-gradient-to-b from-blue-600/0 to-purple-900/30 mix-blend-multiply z-10 pointer-events-none"></div>
                <div className="relative w-full" style={{ paddingBottom: '56.25%' }}>
                  <video 
                    className="absolute inset-0 w-full h-full object-cover"
                    autoPlay 
                    loop 
                    muted 
                    playsInline
                    preload="auto"
                  >
                    <source src="/Epic Entrance.mp4" type="video/mp4" />
                    Your browser does not support the video tag.
                  </video>
                </div>
                <div className="absolute bottom-0 left-0 right-0 p-3 sm:p-4 bg-gradient-to-t from-black/70 to-transparent z-20">
                  <div className="flex flex-col sm:flex-row sm:justify-between sm:items-end">
                    <p className="text-white text-sm sm:text-base font-medium drop-shadow-lg mb-2 sm:mb-0">Experience the magic of Universal's Epic Universe - Now Open!</p>
                    <a href="https://www.universalorlando.com/web/en/us/theme-parks/universal-epic-universe" 
                       target="_blank" rel="noopener noreferrer" 
                       className="inline-flex items-center px-3 py-1.5 bg-blue-600 text-white text-xs font-medium rounded-full shadow-md hover:bg-blue-700 transition-colors">
                      <ExternalLink className="h-3 w-3 mr-1" /> Official Site
                    </a>
                  </div>
                </div>
              </div>
              
              {/* Brand New Attractions Heading */}
              <h3 className="text-center font-bold text-xl md:text-2xl my-4 text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-blue-500 animate-pulse" style={{ textShadow: '0 1px 2px rgba(0,0,0,0.1)' }}>
                BRAND NEW ATTRACTIONS
              </h3>
              
              {/* Single column on mobile, grid on larger screens */}
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-12 gap-2 md:gap-3">
                {/* Ministry of Magic */}
                <div className="col-span-1 md:col-span-6 h-40 sm:h-44 md:h-48 lg:h-52 relative rounded-xl overflow-hidden shadow-md transition-all duration-500 group">
                  <div className="absolute inset-0 bg-gradient-to-b from-amber-600/20 to-amber-900/50 mix-blend-multiply transition-opacity group-hover:opacity-60 z-10"></div>
                  <img 
                    src="/epic-ministry-of-magic.avif" 
                    alt="Universal Epic Universe Ministry of Magic" 
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="absolute bottom-2 left-2 z-20">
                    <span className="text-white text-xs sm:text-sm font-semibold drop-shadow-lg">Ministry of Magic</span>
                  </div>
                </div>
                
                {/* Super Nintendo World */}
                <div className="col-span-1 md:col-span-6 h-40 sm:h-44 md:h-48 lg:h-52 relative rounded-xl overflow-hidden shadow-md transition-all duration-500 group">
                  <div className="absolute inset-0 bg-gradient-to-b from-red-600/20 to-red-900/50 mix-blend-multiply transition-opacity group-hover:opacity-60 z-10"></div>
                  <img 
                    src="/epic-nintendo-world.avif" 
                    alt="Universal Epic Universe Super Nintendo World" 
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="absolute bottom-2 left-2 z-20">
                    <span className="text-white text-xs sm:text-sm font-semibold drop-shadow-lg">Super Nintendo World</span>
                  </div>
                </div>
                
                {/* How to Train Your Dragon - Isle of Berk */}
                <div className="col-span-1 md:col-span-4 h-40 sm:h-44 md:h-40 lg:h-44 relative rounded-xl overflow-hidden shadow-md transition-all duration-500 group">
                  <div className="absolute inset-0 bg-gradient-to-b from-green-600/20 to-green-900/50 mix-blend-multiply transition-opacity group-hover:opacity-60 z-10"></div>
                  <img 
                    src="/epic-isle-of-berk.avif" 
                    alt="Universal Epic Universe Isle of Berk" 
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="absolute bottom-2 left-2 z-20">
                    <span className="text-white text-xs sm:text-sm font-semibold drop-shadow-lg">Isle of Berk</span>
                  </div>
                </div>
                
                {/* Dark Universe */}
                <div className="col-span-1 md:col-span-4 h-40 sm:h-44 md:h-40 lg:h-44 relative rounded-xl overflow-hidden shadow-md transition-all duration-500 group">
                  <div className="absolute inset-0 bg-gradient-to-b from-gray-600/20 to-black/50 mix-blend-multiply transition-opacity group-hover:opacity-60 z-10"></div>
                  <img 
                    src="/epic-dark-universe.avif" 
                    alt="Universal Epic Universe Dark Universe" 
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="absolute bottom-2 left-2 z-20">
                    <span className="text-white text-xs sm:text-sm font-semibold drop-shadow-lg">Dark Universe</span>
                  </div>
                </div>
                
                {/* Celestial Park */}
                <div className="col-span-1 md:col-span-4 h-40 sm:h-44 md:h-40 lg:h-44 relative rounded-xl overflow-hidden shadow-md transition-all duration-500 group">
                  <div className="absolute inset-0 bg-gradient-to-b from-blue-600/20 to-purple-900/50 mix-blend-multiply transition-opacity group-hover:opacity-60 z-10"></div>
                  <img 
                    src="/epic-celestial-park.avif" 
                    alt="Universal Epic Universe Celestial Park" 
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="absolute bottom-2 left-2 z-20">
                    <span className="text-white text-xs sm:text-sm font-semibold drop-shadow-lg">Celestial Park</span>
                  </div>
                </div>
              </div>
              <div className="text-center mt-4">
                <p className="text-sm text-gray-700 italic">Universal's Epic Universe - Orlando's newest theme park - Now Open!</p>
              </div>
            </div>
            
            {/* Universal's Volcano Bay */}
            <div className="group relative rounded-xl overflow-hidden shadow-md transition-all duration-500 hover:shadow-lg border border-red-100 hover:border-red-300">
              <div className="absolute inset-0 bg-gradient-to-b from-orange-600/40 to-red-900/70 mix-blend-multiply transition-opacity group-hover:opacity-70 z-10"></div>
              <img
                src="/Volcano Bay.webp"
                alt="Universal's Volcano Bay"
                className="w-full h-[320px] object-cover transition-transform duration-700 group-hover:scale-110"
              />
              <div className="absolute inset-0 flex flex-col justify-end p-5 z-20">
                <h3 className="text-2xl font-bold text-white mb-1 drop-shadow-lg">Universal's Volcano Bay</h3>
                <p className="text-white text-sm mb-4 drop-shadow-md max-w-lg line-clamp-2">
                  Tropical paradise water theme park featuring TapuTapu wearable technology, a 200-foot volcano, and innovative water attractions.
                </p>
                <a href={themeParkData["volcano-bay"].websiteUrl} target="_blank" rel="noopener noreferrer">
                  <Button size="sm" className="bg-red-600 hover:bg-red-700 gap-1 shadow-lg transform transition hover:translate-y-[-2px]">
                    <Waves className="h-3 w-3" /> Official Site
                  </Button>
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Secondary major theme parks - SeaWorld and LEGOLAND */}
      <section className="mb-16">
        <div className="flex items-center justify-center mb-8">
          <div className="w-12 h-12 rounded-full bg-gradient-to-r from-teal-500 via-purple-500 to-yellow-500 flex items-center justify-center mr-3 shadow-lg">
            <Sparkles className="h-6 w-6 text-white" />
          </div>
          <h2 className="text-3xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-teal-500 via-purple-500 to-yellow-500">More Amazing Adventures</h2>
        </div>
        
        <div className="grid grid-cols-1 gap-8">
          {/* SeaWorld Group with Aquatica and Discovery Cove */}
          <div className="space-y-6">
            <div className="flex items-center mb-4">
              <div className="w-8 h-8 rounded-full bg-gradient-to-r from-teal-500 to-blue-600 flex items-center justify-center mr-2">
                <Waves className="h-4 w-4 text-white" />
              </div>
              <h3 className="text-xl md:text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-teal-500 to-blue-600">SeaWorld Parks & Entertainment</h3>
            </div>
            
            {/* SeaWorld Entry Door */}
            <div className="group relative rounded-xl overflow-hidden shadow-lg transition-all duration-500 hover:shadow-xl border border-teal-200 hover:border-teal-400">
              <div className="absolute inset-0 bg-gradient-to-b from-teal-600/30 to-blue-900/70 mix-blend-multiply transition-opacity group-hover:opacity-70 z-10"></div>
              <img
                src="/Seaworld5.jpg"
                alt="SeaWorld Orlando"
                className="w-full h-[300px] object-cover transition-transform duration-700 group-hover:scale-110"
              />
              <div className="absolute inset-0 flex flex-col justify-end p-6 z-20">
                <h3 className="text-2xl md:text-3xl font-bold text-white mb-2 drop-shadow-lg">SeaWorld Orlando</h3>
                <p className="text-white text-base mb-4 drop-shadow-md max-w-lg line-clamp-2">
                  Experience marine life up close, thrilling coasters, and educational exhibits at Orlando's premier marine theme park.
                </p>
                <div className="flex gap-2">
                  <a href={themeParkData["seaworld"].websiteUrl} target="_blank" rel="noopener noreferrer">
                    <Button className="bg-teal-600 hover:bg-teal-700 gap-2 shadow-lg transform transition hover:translate-y-[-2px]">
                      <ExternalLink className="h-4 w-4" /> Official Site
                    </Button>
                  </a>
                </div>
              </div>
            </div>

            {/* SeaWorld Photo Montage */}
            <div className="my-6">
              <h4 className="text-lg font-medium text-teal-800 mb-3">Experience the Magic of SeaWorld</h4>
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 md:gap-3">
                {/* Dolphin Show */}
                <div className="relative h-40 sm:h-44 md:h-48 rounded-xl overflow-hidden shadow-md transition-all duration-500 group">
                  <div className="absolute inset-0 bg-gradient-to-b from-teal-600/10 to-teal-900/40 mix-blend-multiply transition-opacity group-hover:opacity-60 z-10"></div>
                  <img 
                    src="/Seaworld.png" 
                    alt="SeaWorld Dolphin Show" 
                    className="w-full h-full object-cover object-center transition-transform duration-700 group-hover:scale-110"
                    style={{ objectPosition: "50% 40%" }}
                  />
                  <div className="absolute bottom-2 left-2 z-20">
                    <span className="text-white text-xs sm:text-sm font-semibold drop-shadow-lg">Dolphin Encounter</span>
                  </div>
                </div>
                
                {/* Roller Coaster */}
                <div className="relative h-40 sm:h-44 md:h-48 rounded-xl overflow-hidden shadow-md transition-all duration-500 group">
                  <div className="absolute inset-0 bg-gradient-to-b from-blue-600/10 to-blue-900/40 mix-blend-multiply transition-opacity group-hover:opacity-60 z-10"></div>
                  <img 
                    src="/Seaworld3.webp" 
                    alt="SeaWorld Roller Coaster" 
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="absolute bottom-2 left-2 z-20">
                    <span className="text-white text-xs sm:text-sm font-semibold drop-shadow-lg">Thrilling Coasters</span>
                  </div>
                </div>
                
                {/* Sesame Street */}
                <div className="relative h-40 sm:h-44 md:h-48 rounded-xl overflow-hidden shadow-md transition-all duration-500 group">
                  <div className="absolute inset-0 bg-gradient-to-b from-purple-600/10 to-blue-900/40 mix-blend-multiply transition-opacity group-hover:opacity-60 z-10"></div>
                  <img 
                    src="/Sesame Street.webp" 
                    alt="Sesame Street at SeaWorld" 
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="absolute bottom-2 left-2 z-20">
                    <span className="text-white text-xs sm:text-sm font-semibold drop-shadow-lg">Sesame Street Land</span>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Aquatica and Discovery Cove smaller cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Aquatica Card */}
              <div className="group relative rounded-xl overflow-hidden shadow-md transition-all duration-500 hover:shadow-lg border border-blue-100 hover:border-blue-300">
                <div className="absolute inset-0 bg-gradient-to-b from-blue-600/30 to-teal-900/70 mix-blend-multiply transition-opacity group-hover:opacity-70 z-10"></div>
                <img
                  src="/Aquatica.jpg" 
                  alt="Aquatica Orlando"
                  className="w-full h-[200px] object-cover transition-transform duration-700 group-hover:scale-110"
                />
                <div className="absolute inset-0 flex flex-col justify-end p-4 z-20">
                  <h3 className="text-xl font-bold text-white mb-1 drop-shadow-lg">Aquatica Orlando</h3>
                  <p className="text-white text-sm mb-3 drop-shadow-md max-w-lg line-clamp-2">
                    SeaWorld's water park with thrilling slides and animal encounters in a South Seas-inspired setting.
                  </p>
                  <a href={themeParkData["aquatica"].websiteUrl} target="_blank" rel="noopener noreferrer">
                    <Button size="sm" className="bg-blue-600 hover:bg-blue-700 gap-1 shadow-lg transform transition hover:translate-y-[-2px]">
                      <Waves className="h-3 w-3" /> Visit Aquatica
                    </Button>
                  </a>
                </div>
              </div>
              
              {/* Discovery Cove Card */}
              <div className="group relative rounded-xl overflow-hidden shadow-md transition-all duration-500 hover:shadow-lg border border-emerald-100 hover:border-emerald-300">
                <div className="absolute inset-0 bg-gradient-to-b from-emerald-600/30 to-teal-900/70 mix-blend-multiply transition-opacity group-hover:opacity-70 z-10"></div>
                <img
                  src="/Discovery Cove.jpg"
                  alt="Discovery Cove"
                  className="w-full h-[200px] object-cover transition-transform duration-700 group-hover:scale-110"
                />
                <div className="absolute inset-0 flex flex-col justify-end p-4 z-20">
                  <h3 className="text-xl font-bold text-white mb-1 drop-shadow-lg">Discovery Cove</h3>
                  <p className="text-white text-sm mb-3 drop-shadow-md max-w-lg line-clamp-2">
                    All-inclusive day resort with dolphin swims, snorkeling, and tropical relaxation.
                  </p>
                  <a href={themeParkData["discovery-cove"].websiteUrl} target="_blank" rel="noopener noreferrer">
                    <Button size="sm" className="bg-emerald-600 hover:bg-emerald-700 gap-1 shadow-lg transform transition hover:translate-y-[-2px]">
                      <Droplet className="h-3 w-3" /> Discover Paradise
                    </Button>
                  </a>
                </div>
              </div>
            </div>
          </div>
          
          {/* LEGOLAND Entry Door */}
          <div>
            <div className="flex items-center mb-4">
              <div className="w-8 h-8 rounded-full bg-gradient-to-r from-yellow-500 to-red-500 flex items-center justify-center mr-2">
                <Gamepad2 className="h-4 w-4 text-white" />
              </div>
              <h3 className="text-xl md:text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-yellow-500 to-red-500">LEGOLAND Florida</h3>
            </div>
            <div className="group relative rounded-xl overflow-hidden shadow-lg transition-all duration-500 hover:shadow-xl border border-yellow-200 hover:border-yellow-400">
              <div className="absolute inset-0 bg-gradient-to-b from-yellow-600/30 to-red-900/70 mix-blend-multiply transition-opacity group-hover:opacity-70 z-10"></div>
              <div className="relative w-full pt-[56.25%] overflow-hidden" style={{ background: '#f0f8ff' }}>
                <img 
                  src="/LegoLand3.webp"
                  alt="LEGOLAND Florida"
                  className="absolute inset-0 w-full h-full transition-transform duration-700 group-hover:scale-110"
                  style={{ objectFit: 'contain', transform: 'scale(1.2)' }}
                />
              </div>
              <div className="absolute inset-0 flex flex-col justify-end p-6 z-20">
                <div className="mt-auto">
                  <h3 className="text-2xl md:text-3xl font-bold text-white mb-2 drop-shadow-lg">LEGOLAND Florida</h3>
                  <p className="text-white text-base mb-4 drop-shadow-md max-w-lg line-clamp-2">
                    A colorful family playground with LEGO-themed rides, Peppa Pig Land, shows, and attractions perfect for kids 2-12.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-2 pb-4">
                    <a href={themeParkData["legoland"].websiteUrl} target="_blank" rel="noopener noreferrer">
                      <Button className="bg-yellow-600 hover:bg-yellow-700 gap-2 shadow-lg transform transition hover:translate-y-[-2px]">
                        <ExternalLink className="h-4 w-4" /> Official Site
                      </Button>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>


      <section className="mb-16">
        <div className="flex items-center justify-center mb-8">
          <div className="w-12 h-12 rounded-full bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500 flex items-center justify-center mr-3 shadow-lg">
            <CalendarDays className="h-6 w-6 text-white" />
          </div>
          <h2 className="text-3xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500">Magical Seasonal Events</h2>
        </div>
        <div className="bg-gradient-to-r from-purple-50 via-indigo-50 to-blue-50 rounded-xl p-6 shadow-lg border border-indigo-100">
          <div className="mb-8">
            <div className="flex items-center mb-4">
              <div className="w-8 h-8 rounded-full bg-gradient-to-r from-indigo-400 to-purple-500 flex items-center justify-center mr-2">
                <Sparkles className="h-4 w-4 text-white" />
              </div>
              <h3 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-400 to-purple-500">Major Annual Events</h3>
            </div>
            <div className="bg-white rounded-xl shadow-md overflow-x-auto border-2 border-indigo-100">
              <table className="min-w-full table-auto">
                <thead>
                  <tr className="bg-slate-800 text-white">
                    <th className="px-4 sm:px-6 py-3 text-left text-xs sm:text-sm font-semibold uppercase">Event</th>
                    <th className="px-4 sm:px-6 py-3 text-left text-xs sm:text-sm font-semibold uppercase">Venue</th>
                    <th className="px-4 sm:px-6 py-3 text-left text-xs sm:text-sm font-semibold uppercase">When</th>
                    <th className="px-4 sm:px-6 py-3 text-left text-xs sm:text-sm font-semibold uppercase">Website</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-slate-200">
                  {/* Halloween Horror Nights */}
                  <tr className="hover:bg-slate-50">
                    <td className="px-3 sm:px-6 py-3 text-xs sm:text-sm font-medium text-slate-900">Halloween Horror Nights</td>
                    <td className="px-3 sm:px-6 py-3 text-xs sm:text-sm text-slate-600">Universal Studios Florida</td>
                    <td className="px-3 sm:px-6 py-3 text-xs sm:text-sm text-slate-600">Sept-Oct</td>
                    <td className="px-3 sm:px-6 py-3 text-xs sm:text-sm text-slate-600">
                      <a 
                        href="https://www.universalorlando.com/web/en/us/things-to-do/events/halloween-horror-nights" 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-orange-600 hover:text-orange-700 flex items-center"
                      >
                        <ExternalLink className="h-3.5 w-3.5 mr-1" /> Visit
                      </a>
                    </td>
                  </tr>

                  {/* EPCOT Food & Wine Festival */}
                  <tr className="hover:bg-slate-50">
                    <td className="px-3 sm:px-6 py-3 text-xs sm:text-sm font-medium text-slate-900">EPCOT Food & Wine Festival</td>
                    <td className="px-3 sm:px-6 py-3 text-xs sm:text-sm text-slate-600">EPCOT</td>
                    <td className="px-3 sm:px-6 py-3 text-xs sm:text-sm text-slate-600">Aug-Nov</td>
                    <td className="px-3 sm:px-6 py-3 text-xs sm:text-sm text-slate-600">
                      <a 
                        href="https://disneyworld.disney.go.com/events-tours/epcot/epcot-international-food-and-wine-festival/" 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-orange-600 hover:text-orange-700 flex items-center"
                      >
                        <ExternalLink className="h-3.5 w-3.5 mr-1" /> Visit
                      </a>
                    </td>
                  </tr>

                  {/* Mickey's Not-So-Scary Halloween Party */}
                  <tr className="hover:bg-slate-50">
                    <td className="px-6 py-4 text-sm font-medium text-slate-900">Mickey's Not-So-Scary Halloween Party</td>
                    <td className="px-6 py-4 text-sm text-slate-600">Magic Kingdom</td>
                    <td className="px-6 py-4 text-sm text-slate-600">August-October (select nights)</td>
                    <td className="px-6 py-4 text-sm text-slate-600">
                      <a 
                        href="https://disneyworld.disney.go.com/events-tours/magic-kingdom/mickeys-not-so-scary-halloween-party/" 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-orange-600 hover:text-orange-700 flex items-center"
                      >
                        <ExternalLink className="h-3.5 w-3.5 mr-1" /> Visit
                      </a>
                    </td>
                  </tr>

                  {/* Mickey's Very Merry Christmas Party */}
                  <tr className="hover:bg-slate-50">
                    <td className="px-6 py-4 text-sm font-medium text-slate-900">Mickey's Very Merry Christmas Party</td>
                    <td className="px-6 py-4 text-sm text-slate-600">Magic Kingdom</td>
                    <td className="px-6 py-4 text-sm text-slate-600">November-December (select nights)</td>
                    <td className="px-6 py-4 text-sm text-slate-600">
                      <a 
                        href="https://disneyworld.disney.go.com/events-tours/magic-kingdom/mickeys-very-merry-christmas-party/" 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-orange-600 hover:text-orange-700 flex items-center"
                      >
                        <ExternalLink className="h-3.5 w-3.5 mr-1" /> Visit
                      </a>
                    </td>
                  </tr>

                  {/* EPCOT Flower & Garden Festival */}
                  <tr className="hover:bg-slate-50">
                    <td className="px-6 py-4 text-sm font-medium text-slate-900">EPCOT International Flower & Garden Festival</td>
                    <td className="px-6 py-4 text-sm text-slate-600">EPCOT</td>
                    <td className="px-6 py-4 text-sm text-slate-600">March-July</td>
                    <td className="px-6 py-4 text-sm text-slate-600">
                      <a 
                        href="https://disneyworld.disney.go.com/events-tours/epcot/epcot-international-flower-and-garden-festival/" 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-orange-600 hover:text-orange-700 flex items-center"
                      >
                        <ExternalLink className="h-3.5 w-3.5 mr-1" /> Visit
                      </a>
                    </td>
                  </tr>

                  {/* Universal's Mardi Gras */}
                  <tr className="hover:bg-slate-50">
                    <td className="px-6 py-4 text-sm font-medium text-slate-900">Universal's Mardi Gras</td>
                    <td className="px-6 py-4 text-sm text-slate-600">Universal Studios Florida</td>
                    <td className="px-6 py-4 text-sm text-slate-600">February-April</td>
                    <td className="px-6 py-4 text-sm text-slate-600">
                      <a 
                        href="https://www.universalorlando.com/web/en/us/things-to-do/events/mardi-gras" 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-orange-600 hover:text-orange-700 flex items-center"
                      >
                        <ExternalLink className="h-3.5 w-3.5 mr-1" /> Visit
                      </a>
                    </td>
                  </tr>

                  {/* Holidays at Universal Orlando */}
                  <tr className="hover:bg-slate-50">
                    <td className="px-6 py-4 text-sm font-medium text-slate-900">Holidays at Universal Orlando</td>
                    <td className="px-6 py-4 text-sm text-slate-600">Universal Orlando Resort</td>
                    <td className="px-6 py-4 text-sm text-slate-600">November-January</td>
                    <td className="px-6 py-4 text-sm text-slate-600">
                      <a 
                        href="https://www.universalorlando.com/web/en/us/things-to-do/events/universal-holiday-tour" 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-orange-600 hover:text-orange-700 flex items-center"
                      >
                        <ExternalLink className="h-3.5 w-3.5 mr-1" /> Visit
                      </a>
                    </td>
                  </tr>

                  {/* SeaWorld's Christmas Celebration */}
                  <tr className="hover:bg-slate-50">
                    <td className="px-6 py-4 text-sm font-medium text-slate-900">SeaWorld's Christmas Celebration</td>
                    <td className="px-6 py-4 text-sm text-slate-600">SeaWorld Orlando</td>
                    <td className="px-6 py-4 text-sm text-slate-600">November-December</td>
                    <td className="px-6 py-4 text-sm text-slate-600">
                      <a 
                        href="https://seaworld.com/orlando/events/christmas-celebration/" 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-orange-600 hover:text-orange-700 flex items-center"
                      >
                        <ExternalLink className="h-3.5 w-3.5 mr-1" /> Visit
                      </a>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-8">
            {/* Spring Card */}
            <div className="bg-gradient-to-br from-purple-50 to-indigo-50 p-5 rounded-xl shadow-md border-2 border-purple-100 transform transition-all duration-300 hover:scale-[1.02] hover:shadow-lg group">
              <div className="flex items-center mb-3">
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-purple-400 to-indigo-400 flex items-center justify-center mr-2">
                  <Palmtree className="h-4 w-4 text-white" />
                </div>
                <h3 className="text-lg font-bold text-purple-700 group-hover:text-purple-800">Spring Magic</h3>
              </div>
              <ul className="space-y-2 text-sm">
                <li className="flex items-center"><div className="w-1.5 h-1.5 rounded-full bg-purple-400 mr-2"></div>SeaWorld Seven Seas Food Festival</li>
                <li className="flex items-center"><div className="w-1.5 h-1.5 rounded-full bg-purple-400 mr-2"></div>Easter celebrations at all major parks</li>
                <li className="flex items-center"><div className="w-1.5 h-1.5 rounded-full bg-purple-400 mr-2"></div>Disney's Star Wars Galactic Nights</li>
                <li className="flex items-center"><div className="w-1.5 h-1.5 rounded-full bg-purple-400 mr-2"></div>Discovery Cove Spring Break events</li>
              </ul>
            </div>
            
            {/* Summer Card */}
            <div className="bg-gradient-to-br from-orange-50 to-amber-50 p-5 rounded-xl shadow-md border-2 border-orange-100 transform transition-all duration-300 hover:scale-[1.02] hover:shadow-lg group">
              <div className="flex items-center mb-3">
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-orange-400 to-amber-400 flex items-center justify-center mr-2">
                  <Droplet className="h-4 w-4 text-white" />
                </div>
                <h3 className="text-lg font-bold text-orange-600 group-hover:text-orange-700">Summer Fun</h3>
              </div>
              <ul className="space-y-2 text-sm">
                <li className="flex items-center"><div className="w-1.5 h-1.5 rounded-full bg-orange-400 mr-2"></div>Universal Summer Concert Series</li>
                <li className="flex items-center"><div className="w-1.5 h-1.5 rounded-full bg-orange-400 mr-2"></div>Extended evening hours at all parks</li>
                <li className="flex items-center"><div className="w-1.5 h-1.5 rounded-full bg-orange-400 mr-2"></div>Special summer fireworks displays</li>
                <li className="flex items-center"><div className="w-1.5 h-1.5 rounded-full bg-orange-400 mr-2"></div>Water park peak season operations</li>
              </ul>
            </div>
            
            {/* Fall Card */}
            <div className="bg-gradient-to-br from-amber-50 to-yellow-50 p-5 rounded-xl shadow-md border-2 border-amber-100 transform transition-all duration-300 hover:scale-[1.02] hover:shadow-lg group">
              <div className="flex items-center mb-3">
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-amber-400 to-yellow-400 flex items-center justify-center mr-2">
                  <Sparkles className="h-4 w-4 text-white" />
                </div>
                <h3 className="text-lg font-bold text-amber-700 group-hover:text-amber-800">Fall Adventures</h3>
              </div>
              <ul className="space-y-2 text-sm">
                <li className="flex items-center"><div className="w-1.5 h-1.5 rounded-full bg-amber-400 mr-2"></div>SeaWorld Spooktacular Weekends</li>
                <li className="flex items-center"><div className="w-1.5 h-1.5 rounded-full bg-amber-400 mr-2"></div>LEGOLAND Brick-or-Treat</li>
                <li className="flex items-center"><div className="w-1.5 h-1.5 rounded-full bg-amber-400 mr-2"></div>Gaylord Palms Fall Festivities</li>
                <li className="flex items-center"><div className="w-1.5 h-1.5 rounded-full bg-amber-400 mr-2"></div>Fun Spot's Halloween events</li>
              </ul>
            </div>
            
            {/* Winter Card */}
            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-5 rounded-xl shadow-md border-2 border-blue-100 transform transition-all duration-300 hover:scale-[1.02] hover:shadow-lg group">
              <div className="flex items-center mb-3">
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-400 to-indigo-400 flex items-center justify-center mr-2">
                  <Rocket className="h-4 w-4 text-white" />
                </div>
                <h3 className="text-lg font-bold text-blue-700 group-hover:text-blue-800">Winter Wonders</h3>
              </div>
              <ul className="space-y-2 text-sm">
                <li className="flex items-center"><div className="w-1.5 h-1.5 rounded-full bg-blue-400 mr-2"></div>Epcot Festival of the Holidays</li>
                <li className="flex items-center"><div className="w-1.5 h-1.5 rounded-full bg-blue-400 mr-2"></div>Disney Springs Christmas Tree Trail</li>
                <li className="flex items-center"><div className="w-1.5 h-1.5 rounded-full bg-blue-400 mr-2"></div>LEGOLAND Holidays</li>
                <li className="flex items-center"><div className="w-1.5 h-1.5 rounded-full bg-blue-400 mr-2"></div>ICE! at Gaylord Palms</li>
              </ul>
            </div>
          </div>
        </div>
      </section>


    </div>
  );
}

export default ThemeParkExperienceHardCoded;